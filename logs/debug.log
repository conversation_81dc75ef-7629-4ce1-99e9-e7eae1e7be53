[2m2025-07-28 08:45:42.804[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[kground-preinit][0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m HV000001: Hibernate Validator 8.0.2.Final
[2m2025-07-28 08:45:43.188[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m Starting App using Java 17.0.15 with PID 8951 (/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-server/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-dev)
[2m2025-07-28 08:45:43.191[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m Running with Spring Boot v3.5.3, Spring v6.2.8
[2m2025-07-28 08:45:43.192[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m The following 1 profile is active: "dev"
[2m2025-07-28 08:45:43.329[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.devtools.restart.ChangeableUrls   [0;39m [2m:[0;39m The Class-Path manifest attribute in /Users/<USER>/.m2/repository/javax/mail/mail/1.4/mail-1.4.jar referenced one or more files that do not exist: file:/Users/<USER>/.m2/repository/javax/mail/mail/1.4/activation.jar
[2m2025-07-28 08:45:43.329[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.devtools.restart.ChangeableUrls   [0;39m [2m:[0;39m The Class-Path manifest attribute in /Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar referenced one or more files that do not exist: file:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/oraclepki.jar
[2m2025-07-28 08:45:43.329[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
[2m2025-07-28 08:45:43.330[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
[2m2025-07-28 08:45:45.938[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-07-28 08:45:45.945[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-07-28 08:45:46.407[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 435 ms. Found 10 JPA repository interfaces.
[2m2025-07-28 08:45:46.420[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-07-28 08:45:46.420[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-07-28 08:45:46.460[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.common.repository.BusinessSystemRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-28 08:45:46.460[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-28 08:45:46.460[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-28 08:45:46.461[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.repository.PlatformAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-28 08:45:46.461[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wechat.repository.WechatAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-28 08:45:46.461[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wechat.repository.WechatUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-28 08:45:46.461[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wxwork.repository.WxworkAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-28 08:45:46.461[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wxwork.repository.WxworkUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-28 08:45:46.461[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.yiban.repository.YibanAppRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-28 08:45:46.461[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data MongoDB - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.yiban.repository.YibanUserRepository; If you want this repository to be a MongoDB repository, consider annotating your entities with one of these annotations: org.springframework.data.mongodb.core.mapping.Document (preferred), or consider extending one of the following types with your repository: org.springframework.data.mongodb.repository.MongoRepository
[2m2025-07-28 08:45:46.461[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 40 ms. Found 0 MongoDB repository interfaces.
[2m2025-07-28 08:45:46.475[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-07-28 08:45:46.476[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-07-28 08:45:46.520[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.common.repository.BusinessSystemRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-28 08:45:46.521[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-28 08:45:46.521[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-28 08:45:46.521[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.repository.PlatformAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-28 08:45:46.521[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wechat.repository.WechatAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-28 08:45:46.521[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wechat.repository.WechatUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-28 08:45:46.521[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wxwork.repository.WxworkAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-28 08:45:46.521[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.wxwork.repository.WxworkUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-28 08:45:46.522[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.yiban.repository.YibanAppRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-28 08:45:46.522[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.RepositoryConfigurationExtensionSupport[0;39m [2m:[0;39m Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sanyth.unified_auth.platform.yiban.repository.YibanUserRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
[2m2025-07-28 08:45:46.522[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
[2m2025-07-28 08:45:47.982[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 9096 (http)
[2m2025-07-28 08:45:48.014[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Initializing ProtocolHandler ["http-nio-9096"]
[2m2025-07-28 08:45:48.016[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-07-28 08:45:48.016[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.42]
[2m2025-07-28 08:45:48.091[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-07-28 08:45:48.091[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 4761 ms
[2m2025-07-28 08:45:48.474[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.mongodb.driver.client               [0;39m [2m:[0;39m MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.5.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.15+6-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='admin', source='admin', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@3225b1eb], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@4c55b877, com.mongodb.Jep395RecordCodecProvider@38fdbc9, com.mongodb.KotlinCodecProvider@31adc338]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[*************:37017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@6f614ad4], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
[2m2025-07-28 08:45:48.673[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[168.9.254:37017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=*************:37017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=13, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=124869417, minRoundTripTimeNanos=0}
[2m2025-07-28 08:45:48.740[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join properties config complete
[2m2025-07-28 08:45:48.942[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join SqlInjector init
[2m2025-07-28 08:45:48.947[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.sanyth.core.handler.DecryptInterceptor@582642e0'
[2m2025-07-28 08:45:48.947[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@1d932614'
[2m2025-07-28 08:45:48.947[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.github.yulichang.interceptor.MPJInterceptor@7ea887ee'
[2m2025-07-28 08:45:49.171[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeBjbMapper.xml]'
[2m2025-07-28 08:45:49.225[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeCommonMapper.xml]'
[2m2025-07-28 08:45:49.265[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeDwbMapper.xml]'
[2m2025-07-28 08:45:49.314[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeTypeMapper.xml]'
[2m2025-07-28 08:45:49.356[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/code/mapper/xml/CodeZybMapper.xml]'
[2m2025-07-28 08:45:49.395[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/LoginRecordMapper.xml]'
[2m2025-07-28 08:45:49.428[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/RoleMenuMapper.xml]'
[2m2025-07-28 08:45:49.461[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysAccountMapper.xml]'
[2m2025-07-28 08:45:49.485[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysAccountRoleMapper.xml]'
[2m2025-07-28 08:45:49.534[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysMenuMapper.xml]'
[2m2025-07-28 08:45:49.560[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysParamMapper.xml]'
[2m2025-07-28 08:45:49.587[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysRoleMapper.xml]'
[2m2025-07-28 08:45:49.612[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/SysRoleScopeMapper.xml]'
[2m2025-07-28 08:45:49.667[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform/sanyth-upms/target/classes/com/sanyth/upms/system/mapper/xml/UserInfoMapper.xml]'
[2m2025-07-28 08:45:49.679[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.mybatisplus.core.toolkit.Sequence   [0;39m [2m:[0;39m Initialization Sequence datacenterId:0 workerId:13
[2m2025-07-28 08:45:49.756[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.core.config.MongoConfig      [0;39m [2m:[0;39m GridFSBucket 初始化成功 - 数据库: server, Bucket: fs, 块大小: 255KB
[2m2025-07-28 08:45:49.913[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m Redisson 3.36.0
[2m2025-07-28 08:45:49.996[0;39m [31mERROR[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-07-28 08:45:50.425[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[isson-netty-1-6][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 1 connections initialized for *************/*************:6380
[2m2025-07-28 08:45:53.961[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[sson-netty-1-19][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 24 connections initialized for *************/*************:6380
[2m2025-07-28 08:45:54.522[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.a.security.JwtAuthenticationFilter  [0;39m [2m:[0;39m Filter 'jwtAuthenticationFilter' configured for use
[2m2025-07-28 08:45:54.918[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.hibernate.jpa.internal.util.LogHelper [0;39m [2m:[0;39m HHH000204: Processing PersistenceUnitInfo [name: default]
[2m2025-07-28 08:45:55.035[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.Version                   [0;39m [2m:[0;39m HHH000412: Hibernate ORM core version 6.6.18.Final
[2m2025-07-28 08:45:55.275[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.c.internal.RegionFactoryInitiator   [0;39m [2m:[0;39m HHH000026: Second-level cache disabled
[2m2025-07-28 08:45:55.734[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.o.j.p.SpringPersistenceUnitInfo     [0;39m [2m:[0;39m No LoadTimeWeaver setup: ignoring JPA class transformer
[2m2025-07-28 08:45:56.025[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} inited
[2m2025-07-28 08:45:59.372[0;39m [33m WARN[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.deprecation           [0;39m [2m:[0;39m HHH90000025: OracleDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
[2m2025-07-28 08:45:59.906[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.hibernate.orm.connections.pooling   [0;39m [2m:[0;39m HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource '{
	CreateTime:"2025-07-28 08:45:48",
	ActiveCount:0,
	PoolingCount:1,
	CreateCount:1,
	DestroyCount:0,
	CloseCount:1,
	ConnectCount:1,
	Connections:[
		{ID:976341964, ConnectTime:"2025-07-28 08:45:58", UseCount:1, LastActiveTime:"2025-07-28 08:45:59"}
	]
}']
	Database driver: undefined/unknown
	Database version: 19.3
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
[2m2025-07-28 08:46:01.854[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.e.t.j.p.i.JtaPlatformInitiator      [0;39m [2m:[0;39m HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
[2m2025-07-28 08:46:36.387[0;39m [33m WARN[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.t.s.i.ExceptionHandlerLoggedImpl    [0;39m [2m:[0;39m GenerationTarget encountered exception accepting command : Error executing DDL "
    alter table oauth_client_details 
       modify scope VARCHAR2(255)" via JDBC [ORA-00904: : 标识符无效
]

org.hibernate.tool.schema.spi.CommandAcceptanceException: Error executing DDL "
    alter table oauth_client_details 
       modify scope VARCHAR2(255)" via JDBC [ORA-00904: : 标识符无效
]
	at org.hibernate.tool.schema.internal.exec.GenerationTargetToDatabase.accept(GenerationTargetToDatabase.java:94)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.applySqlString(AbstractSchemaMigrator.java:576)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.applySqlStrings(AbstractSchemaMigrator.java:516)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.migrateTable(AbstractSchemaMigrator.java:334)
	at org.hibernate.tool.schema.internal.GroupedSchemaMigratorImpl.performTablesMigration(GroupedSchemaMigratorImpl.java:84)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.performMigration(AbstractSchemaMigrator.java:233)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.doMigration(AbstractSchemaMigrator.java:112)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.performDatabaseAction(SchemaManagementToolCoordinator.java:280)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.lambda$process$5(SchemaManagementToolCoordinator.java:144)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.process(SchemaManagementToolCoordinator.java:141)
	at org.hibernate.boot.internal.SessionFactoryObserverForSchemaExport.sessionFactoryCreated(SessionFactoryObserverForSchemaExport.java:37)
	at org.hibernate.internal.SessionFactoryObserverChain.sessionFactoryCreated(SessionFactoryObserverChain.java:35)
	at org.hibernate.internal.SessionFactoryImpl.<init>(SessionFactoryImpl.java:324)
	at org.hibernate.boot.internal.SessionFactoryBuilderImpl.__build(SessionFactoryBuilderImpl.java:463)
	at org.hibernate.boot.internal.SessionFactoryBuilderImpl.build(SessionFactoryBuilderImpl.java:41010)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.__build(EntityManagerFactoryBuilderImpl.java:1517)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:40002)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.__createEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:66)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:40003)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.__createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:390)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:46002)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:419)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:400)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1873)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1822)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:970)
	at org.springframework.context.support.AbstractApplicationContext.__refresh(AbstractApplicationContext.java:627)
	at org.springframework.context.support.AbstractApplicationContext.jrLockAndRefresh(AbstractApplicationContext.java:41002)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:42008)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.sanyth.App.main(App.java:20)
Caused by: java.sql.SQLSyntaxErrorException: ORA-00904: : 标识符无效

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:702)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:608)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1248)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:1041)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:443)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:518)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:156)
	at oracle.jdbc.driver.T4CStatement.executeForRows(T4CStatement.java:1085)
	at oracle.jdbc.driver.OracleStatement.executeSQLStatement(OracleStatement.java:1571)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1345)
	at oracle.jdbc.driver.OracleStatement.executeInternal(OracleStatement.java:2198)
	at oracle.jdbc.driver.OracleStatement.execute(OracleStatement.java:2147)
	at oracle.jdbc.driver.OracleStatementWrapper.execute(OracleStatementWrapper.java:330)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:635)
	at org.hibernate.tool.schema.internal.exec.GenerationTargetToDatabase.accept(GenerationTargetToDatabase.java:80)
	... 43 common frames omitted
Caused by: oracle.jdbc.OracleDatabaseException: ORA-00904: : 标识符无效

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:710)
	... 57 common frames omitted

[2m2025-07-28 08:46:37.961[0;39m [33m WARN[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.t.s.i.ExceptionHandlerLoggedImpl    [0;39m [2m:[0;39m GenerationTarget encountered exception accepting command : Error executing DDL "
    alter table syt_aigc_tool 
       modify status NUMBER(1)" via JDBC [ORA-01439: 要更改数据类型, 则要修改的列必须为空
]

org.hibernate.tool.schema.spi.CommandAcceptanceException: Error executing DDL "
    alter table syt_aigc_tool 
       modify status NUMBER(1)" via JDBC [ORA-01439: 要更改数据类型, 则要修改的列必须为空
]
	at org.hibernate.tool.schema.internal.exec.GenerationTargetToDatabase.accept(GenerationTargetToDatabase.java:94)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.applySqlString(AbstractSchemaMigrator.java:576)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.applySqlStrings(AbstractSchemaMigrator.java:516)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.migrateTable(AbstractSchemaMigrator.java:334)
	at org.hibernate.tool.schema.internal.GroupedSchemaMigratorImpl.performTablesMigration(GroupedSchemaMigratorImpl.java:84)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.performMigration(AbstractSchemaMigrator.java:233)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.doMigration(AbstractSchemaMigrator.java:112)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.performDatabaseAction(SchemaManagementToolCoordinator.java:280)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.lambda$process$5(SchemaManagementToolCoordinator.java:144)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.process(SchemaManagementToolCoordinator.java:141)
	at org.hibernate.boot.internal.SessionFactoryObserverForSchemaExport.sessionFactoryCreated(SessionFactoryObserverForSchemaExport.java:37)
	at org.hibernate.internal.SessionFactoryObserverChain.sessionFactoryCreated(SessionFactoryObserverChain.java:35)
	at org.hibernate.internal.SessionFactoryImpl.<init>(SessionFactoryImpl.java:324)
	at org.hibernate.boot.internal.SessionFactoryBuilderImpl.__build(SessionFactoryBuilderImpl.java:463)
	at org.hibernate.boot.internal.SessionFactoryBuilderImpl.build(SessionFactoryBuilderImpl.java:41010)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.__build(EntityManagerFactoryBuilderImpl.java:1517)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:40002)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.__createEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:66)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:40003)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.__createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:390)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:46002)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:419)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:400)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1873)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1822)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:970)
	at org.springframework.context.support.AbstractApplicationContext.__refresh(AbstractApplicationContext.java:627)
	at org.springframework.context.support.AbstractApplicationContext.jrLockAndRefresh(AbstractApplicationContext.java:41002)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:42008)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.sanyth.App.main(App.java:20)
Caused by: java.sql.SQLException: ORA-01439: 要更改数据类型, 则要修改的列必须为空

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:702)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:608)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1248)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:1041)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:443)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:518)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:156)
	at oracle.jdbc.driver.T4CStatement.executeForRows(T4CStatement.java:1085)
	at oracle.jdbc.driver.OracleStatement.executeSQLStatement(OracleStatement.java:1571)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1345)
	at oracle.jdbc.driver.OracleStatement.executeInternal(OracleStatement.java:2198)
	at oracle.jdbc.driver.OracleStatement.execute(OracleStatement.java:2147)
	at oracle.jdbc.driver.OracleStatementWrapper.execute(OracleStatementWrapper.java:330)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:635)
	at org.hibernate.tool.schema.internal.exec.GenerationTargetToDatabase.accept(GenerationTargetToDatabase.java:80)
	... 43 common frames omitted
Caused by: oracle.jdbc.OracleDatabaseException: ORA-01439: 要更改数据类型, 则要修改的列必须为空

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:710)
	... 57 common frames omitted

[2m2025-07-28 08:46:39.723[0;39m [33m WARN[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.t.s.i.ExceptionHandlerLoggedImpl    [0;39m [2m:[0;39m GenerationTarget encountered exception accepting command : Error executing DDL "
    alter table syt_sys_account 
       modify id VARCHAR2(50)" via JDBC [ORA-01439: 要更改数据类型, 则要修改的列必须为空
]

org.hibernate.tool.schema.spi.CommandAcceptanceException: Error executing DDL "
    alter table syt_sys_account 
       modify id VARCHAR2(50)" via JDBC [ORA-01439: 要更改数据类型, 则要修改的列必须为空
]
	at org.hibernate.tool.schema.internal.exec.GenerationTargetToDatabase.accept(GenerationTargetToDatabase.java:94)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.applySqlString(AbstractSchemaMigrator.java:576)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.applySqlStrings(AbstractSchemaMigrator.java:516)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.migrateTable(AbstractSchemaMigrator.java:334)
	at org.hibernate.tool.schema.internal.GroupedSchemaMigratorImpl.performTablesMigration(GroupedSchemaMigratorImpl.java:84)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.performMigration(AbstractSchemaMigrator.java:233)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.doMigration(AbstractSchemaMigrator.java:112)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.performDatabaseAction(SchemaManagementToolCoordinator.java:280)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.lambda$process$5(SchemaManagementToolCoordinator.java:144)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.process(SchemaManagementToolCoordinator.java:141)
	at org.hibernate.boot.internal.SessionFactoryObserverForSchemaExport.sessionFactoryCreated(SessionFactoryObserverForSchemaExport.java:37)
	at org.hibernate.internal.SessionFactoryObserverChain.sessionFactoryCreated(SessionFactoryObserverChain.java:35)
	at org.hibernate.internal.SessionFactoryImpl.<init>(SessionFactoryImpl.java:324)
	at org.hibernate.boot.internal.SessionFactoryBuilderImpl.__build(SessionFactoryBuilderImpl.java:463)
	at org.hibernate.boot.internal.SessionFactoryBuilderImpl.build(SessionFactoryBuilderImpl.java:41010)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.__build(EntityManagerFactoryBuilderImpl.java:1517)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:40002)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.__createEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:66)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:40003)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.__createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:390)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:46002)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:419)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:400)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1873)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1822)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:970)
	at org.springframework.context.support.AbstractApplicationContext.__refresh(AbstractApplicationContext.java:627)
	at org.springframework.context.support.AbstractApplicationContext.jrLockAndRefresh(AbstractApplicationContext.java:41002)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:42008)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.sanyth.App.main(App.java:20)
Caused by: java.sql.SQLException: ORA-01439: 要更改数据类型, 则要修改的列必须为空

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:702)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:608)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1248)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:1041)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:443)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:518)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:156)
	at oracle.jdbc.driver.T4CStatement.executeForRows(T4CStatement.java:1085)
	at oracle.jdbc.driver.OracleStatement.executeSQLStatement(OracleStatement.java:1571)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1345)
	at oracle.jdbc.driver.OracleStatement.executeInternal(OracleStatement.java:2198)
	at oracle.jdbc.driver.OracleStatement.execute(OracleStatement.java:2147)
	at oracle.jdbc.driver.OracleStatementWrapper.execute(OracleStatementWrapper.java:330)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:635)
	at org.hibernate.tool.schema.internal.exec.GenerationTargetToDatabase.accept(GenerationTargetToDatabase.java:80)
	... 43 common frames omitted
Caused by: oracle.jdbc.OracleDatabaseException: ORA-01439: 要更改数据类型, 则要修改的列必须为空

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:710)
	... 57 common frames omitted

[2m2025-07-28 08:46:41.000[0;39m [33m WARN[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.h.t.s.i.ExceptionHandlerLoggedImpl    [0;39m [2m:[0;39m GenerationTarget encountered exception accepting command : Error executing DDL "
    alter table syt_sys_param 
       modify param_name VARCHAR2(100) UNIQUE" via JDBC [ORA-02261: 表中已存在这样的唯一关键字或主键
]

org.hibernate.tool.schema.spi.CommandAcceptanceException: Error executing DDL "
    alter table syt_sys_param 
       modify param_name VARCHAR2(100) UNIQUE" via JDBC [ORA-02261: 表中已存在这样的唯一关键字或主键
]
	at org.hibernate.tool.schema.internal.exec.GenerationTargetToDatabase.accept(GenerationTargetToDatabase.java:94)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.applySqlString(AbstractSchemaMigrator.java:576)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.applySqlStrings(AbstractSchemaMigrator.java:516)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.migrateTable(AbstractSchemaMigrator.java:334)
	at org.hibernate.tool.schema.internal.GroupedSchemaMigratorImpl.performTablesMigration(GroupedSchemaMigratorImpl.java:84)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.performMigration(AbstractSchemaMigrator.java:233)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.doMigration(AbstractSchemaMigrator.java:112)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.performDatabaseAction(SchemaManagementToolCoordinator.java:280)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.lambda$process$5(SchemaManagementToolCoordinator.java:144)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.process(SchemaManagementToolCoordinator.java:141)
	at org.hibernate.boot.internal.SessionFactoryObserverForSchemaExport.sessionFactoryCreated(SessionFactoryObserverForSchemaExport.java:37)
	at org.hibernate.internal.SessionFactoryObserverChain.sessionFactoryCreated(SessionFactoryObserverChain.java:35)
	at org.hibernate.internal.SessionFactoryImpl.<init>(SessionFactoryImpl.java:324)
	at org.hibernate.boot.internal.SessionFactoryBuilderImpl.__build(SessionFactoryBuilderImpl.java:463)
	at org.hibernate.boot.internal.SessionFactoryBuilderImpl.build(SessionFactoryBuilderImpl.java:41010)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.__build(EntityManagerFactoryBuilderImpl.java:1517)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:40002)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.__createEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:66)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:40003)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.__createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:390)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:46002)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:419)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:400)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1873)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1822)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:970)
	at org.springframework.context.support.AbstractApplicationContext.__refresh(AbstractApplicationContext.java:627)
	at org.springframework.context.support.AbstractApplicationContext.jrLockAndRefresh(AbstractApplicationContext.java:41002)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:42008)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.sanyth.App.main(App.java:20)
Caused by: java.sql.SQLSyntaxErrorException: ORA-02261: 表中已存在这样的唯一关键字或主键

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:702)
	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:608)
	at oracle.jdbc.driver.T4C8Oall.processError(T4C8Oall.java:1248)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:1041)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:443)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:518)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:156)
	at oracle.jdbc.driver.T4CStatement.executeForRows(T4CStatement.java:1085)
	at oracle.jdbc.driver.OracleStatement.executeSQLStatement(OracleStatement.java:1571)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1345)
	at oracle.jdbc.driver.OracleStatement.executeInternal(OracleStatement.java:2198)
	at oracle.jdbc.driver.OracleStatement.execute(OracleStatement.java:2147)
	at oracle.jdbc.driver.OracleStatementWrapper.execute(OracleStatementWrapper.java:330)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:635)
	at org.hibernate.tool.schema.internal.exec.GenerationTargetToDatabase.accept(GenerationTargetToDatabase.java:80)
	... 43 common frames omitted
Caused by: oracle.jdbc.OracleDatabaseException: ORA-02261: 表中已存在这样的唯一关键字或主键

	at oracle.jdbc.driver.T4CTTIoer11.processError(T4CTTIoer11.java:710)
	... 57 common frames omitted

[2m2025-07-28 08:46:44.636[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Initialized JPA EntityManagerFactory for persistence unit 'default'
[2m2025-07-28 08:46:46.017[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TYPE,MODEL,PROVIDER,NAME,RESPONSE_LIMIT,TEMPERATURE,TOP_P,API_KEY,BASE_URL,SECRET_KEY,ENDPOINT,IMAGE_SIZE,IMAGE_QUALITY,IMAGE_STYLE,DIMENSIONS,STATUS    FROM  SYT_AIGC_MODEL
[2m2025-07-28 08:46:46.101[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TYPE,MODEL,PROVIDER,NAME,RESPONSE_LIMIT,TEMPERATURE,TOP_P,API_KEY,BASE_URL,SECRET_KEY,ENDPOINT,IMAGE_SIZE,IMAGE_QUALITY,IMAGE_STYLE,DIMENSIONS,STATUS    FROM  SYT_AIGC_MODEL
[2m2025-07-28 08:46:46.106[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TYPE, MODEL, PROVIDER, NAME, RESPONSE_LIMIT, TEMPERATURE, TOP_P, API_KEY, BASE_URL, SECRET_KEY, ENDPOINT, IMAGE_SIZE, IMAGE_QUALITY, IMAGE_STYLE, DIMENSIONS, STATUS FROM SYT_AIGC_MODEL
[2m2025-07-28 08:46:46.119[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TYPE, MODEL, PROVIDER, NAME, RESPONSE_LIMIT, TEMPERATURE, TOP_P, API_KEY, BASE_URL, SECRET_KEY, ENDPOINT, IMAGE_SIZE, IMAGE_QUALITY, IMAGE_STYLE, DIMENSIONS, STATUS FROM SYT_AIGC_MODEL
[2m2025-07-28 08:46:46.130[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 08:46:46.351[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m <==      Total: 4
[2m2025-07-28 08:46:46.653[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.core.provider.ModelStoreFactory   [0;39m [2m:[0;39m 已成功注册模型：OLLAMA -- CHAT， 模型配置：AigcModel(id=b69a8765b69ccd2c59e646a926f55fb2, type=CHAT, model=qwen2.5, provider=OLLAMA, name=Ollama/qwen2.5, responseLimit=2033, temperature=0.2, topP=0.3, apiKey=null, baseUrl=http://localhost:11434/, secretKey=null, endpoint=null, imageSize=null, imageQuality=null, imageStyle=null, dimensions=null, status=enabled)
[2m2025-07-28 08:46:46.657[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.core.provider.ModelStoreFactory   [0;39m [2m:[0;39m 已成功注册模型：OPENAI -- CHAT， 模型配置：AigcModel(id=eddd81d38381ff475f735e539b163e74, type=CHAT, model=Qwen/Qwen3-8B, provider=OPENAI, name=Qwen3-8B, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=sk-heqiftpvnebkqetwsszbclmrneqhwuildjwuvujtdhkeiqnf, baseUrl=https://api.siliconflow.cn/v1, secretKey=null, endpoint=null, imageSize=null, imageQuality=null, imageStyle=null, dimensions=null, status=enabled)
[2m2025-07-28 08:46:46.657[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.core.provider.ModelStoreFactory   [0;39m [2m:[0;39m 已成功注册模型：OPENAI -- EMBEDDING， 模型配置：AigcModel(id=097a00062ad6110d1c86e6fcc1391791, type=EMBEDDING, model=BAAI/bge-m3, provider=OPENAI, name=bge-m3, responseLimit=2000, temperature=0.2, topP=0.5, apiKey=sk-heqiftpvnebkqetwsszbclmrneqhwuildjwuvujtdhkeiqnf, baseUrl=https://api.siliconflow.cn/v1, secretKey=null, endpoint=null, imageSize=null, imageQuality=null, imageStyle=null, dimensions=null, status=enabled)
[2m2025-07-28 08:46:46.657[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.core.provider.ModelStoreFactory   [0;39m [2m:[0;39m 已成功注册模型：OLLAMA -- EMBEDDING， 模型配置：AigcModel(id=618b4c8f87bca57686748264817b34dc, type=EMBEDDING, model=bge-m3, provider=OLLAMA, name=Ollama/bge-m3, responseLimit=2000, temperature=0.2, topP=0.8, apiKey=null, baseUrl=http://localhost:11434/, secretKey=null, endpoint=null, imageSize=null, imageQuality=null, imageStyle=null, dimensions=null, status=enabled)
[2m2025-07-28 08:46:46.743[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,PROVIDER,HOST,USERNAME,DIMENSION,DATABASE_NAME,PORT,TABLE_NAME,PASSWORD    FROM  SYT_AIGC_EMBED_STORE
[2m2025-07-28 08:46:46.749[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,PROVIDER,HOST,USERNAME,DIMENSION,DATABASE_NAME,PORT,TABLE_NAME,PASSWORD    FROM  SYT_AIGC_EMBED_STORE
[2m2025-07-28 08:46:46.750[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, PROVIDER, HOST, USERNAME, DIMENSION, DATABASE_NAME, PORT, TABLE_NAME, PASSWORD FROM SYT_AIGC_EMBED_STORE
[2m2025-07-28 08:46:46.751[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, PROVIDER, HOST, USERNAME, DIMENSION, DATABASE_NAME, PORT, TABLE_NAME, PASSWORD FROM SYT_AIGC_EMBED_STORE
[2m2025-07-28 08:46:46.751[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 08:46:46.845[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 08:46:47.617[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.c.provider.EmbeddingStoreFactory  [0;39m [2m:[0;39m 已成功注册Embedding Store：PGVECTOR， 配置信息：AigcEmbedStore(id=8eead31ed55dadfd634f6118cad0e5c7, name=Pgvector, provider=PGVECTOR, host=*************, username=root, dimension=1024, databaseName=langchain, port=5432, tableName=langchain_store, password=langchain123456)
[2m2025-07-28 08:46:47.714[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,EMBED_STORE_ID,EMBED_MODEL_ID,COVER,DES,CREATE_TIME,MAX_SEGMENT_SIZE,MAX_OVERLAP_SIZE    FROM  SYT_AIGC_KNOWLEDGE
[2m2025-07-28 08:46:47.719[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,EMBED_STORE_ID,EMBED_MODEL_ID,COVER,DES,CREATE_TIME,MAX_SEGMENT_SIZE,MAX_OVERLAP_SIZE    FROM  SYT_AIGC_KNOWLEDGE
[2m2025-07-28 08:46:47.720[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, EMBED_STORE_ID, EMBED_MODEL_ID, COVER, DES, CREATE_TIME, MAX_SEGMENT_SIZE, MAX_OVERLAP_SIZE FROM SYT_AIGC_KNOWLEDGE
[2m2025-07-28 08:46:47.720[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, EMBED_STORE_ID, EMBED_MODEL_ID, COVER, DES, CREATE_TIME, MAX_SEGMENT_SIZE, MAX_OVERLAP_SIZE FROM SYT_AIGC_KNOWLEDGE
[2m2025-07-28 08:46:47.721[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 08:46:47.779[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m <==      Total: 7
[2m2025-07-28 08:46:47.779[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TYPE,MODEL,PROVIDER,NAME,RESPONSE_LIMIT,TEMPERATURE,TOP_P,API_KEY,BASE_URL,SECRET_KEY,ENDPOINT,IMAGE_SIZE,IMAGE_QUALITY,IMAGE_STYLE,DIMENSIONS,STATUS    FROM  SYT_AIGC_MODEL
[2m2025-07-28 08:46:47.786[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TYPE,MODEL,PROVIDER,NAME,RESPONSE_LIMIT,TEMPERATURE,TOP_P,API_KEY,BASE_URL,SECRET_KEY,ENDPOINT,IMAGE_SIZE,IMAGE_QUALITY,IMAGE_STYLE,DIMENSIONS,STATUS    FROM  SYT_AIGC_MODEL
[2m2025-07-28 08:46:47.787[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TYPE, MODEL, PROVIDER, NAME, RESPONSE_LIMIT, TEMPERATURE, TOP_P, API_KEY, BASE_URL, SECRET_KEY, ENDPOINT, IMAGE_SIZE, IMAGE_QUALITY, IMAGE_STYLE, DIMENSIONS, STATUS FROM SYT_AIGC_MODEL
[2m2025-07-28 08:46:47.788[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TYPE, MODEL, PROVIDER, NAME, RESPONSE_LIMIT, TEMPERATURE, TOP_P, API_KEY, BASE_URL, SECRET_KEY, ENDPOINT, IMAGE_SIZE, IMAGE_QUALITY, IMAGE_STYLE, DIMENSIONS, STATUS FROM SYT_AIGC_MODEL
[2m2025-07-28 08:46:47.788[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 08:46:47.847[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcModelMapper.selectList    [0;39m [2m:[0;39m <==      Total: 4
[2m2025-07-28 08:46:47.848[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,NAME,PROVIDER,HOST,USERNAME,DIMENSION,DATABASE_NAME,PORT,TABLE_NAME,PASSWORD    FROM  SYT_AIGC_EMBED_STORE
[2m2025-07-28 08:46:47.853[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,NAME,PROVIDER,HOST,USERNAME,DIMENSION,DATABASE_NAME,PORT,TABLE_NAME,PASSWORD    FROM  SYT_AIGC_EMBED_STORE
[2m2025-07-28 08:46:47.855[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, NAME, PROVIDER, HOST, USERNAME, DIMENSION, DATABASE_NAME, PORT, TABLE_NAME, PASSWORD FROM SYT_AIGC_EMBED_STORE
[2m2025-07-28 08:46:47.855[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, NAME, PROVIDER, HOST, USERNAME, DIMENSION, DATABASE_NAME, PORT, TABLE_NAME, PASSWORD FROM SYT_AIGC_EMBED_STORE
[2m2025-07-28 08:46:47.855[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 08:46:47.907[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.A.selectList                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 08:46:48.121[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.langchain.api.store.AppChannelStore [0;39m [2m:[0;39m 初始化应用程序频道配置列表...
[2m2025-07-28 08:46:48.122[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,APP_ID,API_KEY,CREATE_TIME,CHANNEL    FROM  SYT_AIGC_APP_API
[2m2025-07-28 08:46:48.126[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,APP_ID,API_KEY,CREATE_TIME,CHANNEL    FROM  SYT_AIGC_APP_API
[2m2025-07-28 08:46:48.127[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, APP_ID, API_KEY, CREATE_TIME, CHANNEL FROM SYT_AIGC_APP_API
[2m2025-07-28 08:46:48.127[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcAppApiMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, APP_ID, API_KEY, CREATE_TIME, CHANNEL FROM SYT_AIGC_APP_API
[2m2025-07-28 08:46:48.128[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcAppApiMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 08:46:48.198[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.m.AigcAppApiMapper.selectList   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-28 08:46:48.199[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.langchain.api.store.AppChannelStore [0;39m [2m:[0;39m 成功初始化 1 API通道
[2m2025-07-28 08:46:48.253[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.sanyth.langchain.api.store.AppStore   [0;39m [2m:[0;39m Initializing app config list...
[2m2025-07-28 08:46:48.254[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,CLASS_ID,DES,PROMPT,NAME,CREATE_TIME,SAVE_TIME,COVER,KNOWLEDGE_IDS,TOOL_IDS,MCP_SERVER_ID,MODEL_ID,PROLOGUE,STATUS,MAX_RESULTS,MIN_SCORE,PRESET_QUESTION    FROM  SYT_AIGC_APP
[2m2025-07-28 08:46:48.259[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,CLASS_ID,DES,PROMPT,NAME,CREATE_TIME,SAVE_TIME,COVER,KNOWLEDGE_IDS,TOOL_IDS,MCP_SERVER_ID,MODEL_ID,PROLOGUE,STATUS,MAX_RESULTS,MIN_SCORE,PRESET_QUESTION    FROM  SYT_AIGC_APP
[2m2025-07-28 08:46:48.260[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, CLASS_ID, DES, PROMPT, NAME, CREATE_TIME, SAVE_TIME, COVER, KNOWLEDGE_IDS, TOOL_IDS, MCP_SERVER_ID, MODEL_ID, PROLOGUE, STATUS, MAX_RESULTS, MIN_SCORE, PRESET_QUESTION FROM SYT_AIGC_APP
[2m2025-07-28 08:46:48.260[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.mapper.AigcAppMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, CLASS_ID, DES, PROMPT, NAME, CREATE_TIME, SAVE_TIME, COVER, KNOWLEDGE_IDS, TOOL_IDS, MCP_SERVER_ID, MODEL_ID, PROLOGUE, STATUS, MAX_RESULTS, MIN_SCORE, PRESET_QUESTION FROM SYT_AIGC_APP
[2m2025-07-28 08:46:48.261[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.mapper.AigcAppMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-28 08:46:48.395[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.l.a.mapper.AigcAppMapper.selectList [0;39m [2m:[0;39m <==      Total: 3
[2m2025-07-28 08:46:48.396[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.sanyth.langchain.api.store.AppStore   [0;39m [2m:[0;39m Successfully initialized 3 apps
[2m2025-07-28 08:46:49.181[0;39m [33m WARN[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mJpaBaseConfiguration$JpaWebConfiguration[0;39m [2m:[0;39m spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
[2m2025-07-28 08:46:49.629[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.a.e.web.EndpointLinksResolver     [0;39m [2m:[0;39m Exposing 1 endpoint beneath base path '/actuator'
[2m2025-07-28 08:46:49.678[0;39m [33m WARN[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.s.UserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 1ec74ed8-bcb6-4aae-add8-4810e11fe722

This generated password is for development use only. Your security configuration must be updated before running your application in production.

[2m2025-07-28 08:46:49.690[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mr$InitializeUserDetailsManagerConfigurer[0;39m [2m:[0;39m Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
[2m2025-07-28 08:46:50.122[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.d.a.OptionalLiveReloadServer      [0;39m [2m:[0;39m LiveReload server is running on port 35729
[2m2025-07-28 08:46:50.419[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mctiveUserDetailsServiceAutoConfiguration[0;39m [2m:[0;39m 

Using generated security password: 1ec74ed8-bcb6-4aae-add8-4810e11fe722

[2m2025-07-28 08:46:50.565[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Starting ProtocolHandler ["http-nio-9096"]
[2m2025-07-28 08:46:50.611[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat started on port 9096 (http) with context path '/'
[2m2025-07-28 08:46:50.625[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.sanyth.App                          [0;39m [2m:[0;39m Started App in 68.829 seconds (process running for 70.936)
[2m2025-07-28 08:46:50.635[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL start create  ...  
[2m2025-07-28 08:46:50.636[0;39m [32mDEBUG[0;39m [35m8951[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.a.DdlApplicationRunner            [0;39m [2m:[0;39m   ...  DDL end create  ...  
[2m2025-07-28 08:47:23.064[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m [2m:[0;39m Commencing graceful shutdown. Waiting for active requests to complete
[2m2025-07-28 08:47:23.076[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[tomcat-shutdown][0;39m [36mo.s.b.w.e.tomcat.GracefulShutdown       [0;39m [2m:[0;39m Graceful shutdown complete
[2m2025-07-28 08:47:23.134[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mj.LocalContainerEntityManagerFactoryBean[0;39m [2m:[0;39m Closing JPA EntityManagerFactory for persistence unit 'default'
[2m2025-07-28 08:47:23.165[0;39m [32m INFO[0;39m [35m8951[0;39m [2m---[0;39m [2m[ionShutdownHook][0;39m [36mcom.alibaba.druid.pool.DruidDataSource  [0;39m [2m:[0;39m {dataSource-1} closing ...
