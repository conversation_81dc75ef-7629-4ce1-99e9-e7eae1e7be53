package com.sanyth.auth.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanyth.core.annotation.QueryField;
import com.sanyth.core.annotation.QueryType;
import com.sanyth.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OauthClientDetailsParam extends BaseParam {

    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;
    @QueryField(type = QueryType.EQ)
    private String clientId;
    private String resourceIds;
    @QueryField(type = QueryType.EQ)
    private String clientSecret;

    @QueryField(type = QueryType.LIKE)
    private String clientName;

}
