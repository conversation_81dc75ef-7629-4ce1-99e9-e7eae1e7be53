package com.sanyth.auth.security;

import jakarta.servlet.http.HttpServletRequest;

/**
 * @since 2025/7/2 15:05
 */
public class UnifiedAuthFilterUtil {
    public static final String SKIP_JWT_ATTR = "skipJwt";

    public static void markSkipJwt(HttpServletRequest request) {
//        System.out.println("markSkipJwt");
        request.setAttribute(SKIP_JWT_ATTR, true);
    }

    public static boolean shouldSkipJwt(HttpServletRequest request) {
//        System.out.println("shouldSkipJwt");
        return Boolean.TRUE.equals(request.getAttribute(SKIP_JWT_ATTR));
    }
}
