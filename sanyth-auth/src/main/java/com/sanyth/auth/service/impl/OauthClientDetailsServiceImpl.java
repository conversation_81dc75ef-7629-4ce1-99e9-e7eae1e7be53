package com.sanyth.auth.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.auth.entity.OauthClientDetails;
import com.sanyth.auth.mapper.OauthClientDetailsMapper;
import com.sanyth.auth.param.OauthClientDetailsParam;
import com.sanyth.auth.service.OauthClientDetailsService;
import com.sanyth.core.web.PageParam;
import com.sanyth.core.web.PageResult;
import org.springframework.stereotype.Service;

@Service
public class OauthClientDetailsServiceImpl extends ServiceImpl<OauthClientDetailsMapper, OauthClientDetails> implements OauthClientDetailsService {


    @Override
    public PageResult<OauthClientDetails> queryPage(OauthClientDetailsParam param) {
        PageParam<OauthClientDetails, OauthClientDetailsParam> page = new PageParam<>(param);
        page = baseMapper.selectPage(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }
}
