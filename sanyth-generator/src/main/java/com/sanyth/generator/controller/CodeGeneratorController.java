package com.sanyth.generator.controller;

import com.sanyth.core.annotation.OperationLog;
import com.sanyth.core.web.BaseController;
import com.sanyth.core.web.PageParam;
import com.sanyth.core.web.PageResult;
import com.sanyth.generator.model.DataSourceConfig;
import com.sanyth.generator.model.GenConfig;
import com.sanyth.generator.param.ColumnInfoParam;
import com.sanyth.generator.param.TableInfoParam;
import com.sanyth.generator.service.CodeGeneratorService;
import com.sanyth.generator.vo.ColumnInfo;
import com.sanyth.generator.vo.TableInfo;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;



/**
 * 代码生成控制器
 *
 * <AUTHOR>
 * @since 2024-02-19
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/generator")
public class CodeGeneratorController extends BaseController {

    private final CodeGeneratorService generatorService;

    /**
     * 获取数据库表信息
     */
    @GetMapping("/tables")
    public PageResult<TableInfo> getTables(DataSourceConfig dataSource, String tableName,TableInfoParam param) {
        PageParam<TableInfo, TableInfoParam> pageParam = new PageParam<>(param);
        return generatorService.pageTable(dataSource, tableName, pageParam);
    }

    /**
     * 获取表字段信息
     */
    @GetMapping("/columns")
    public PageResult<ColumnInfo> getColumns(DataSourceConfig dataSource, String tableName,ColumnInfoParam param) {
        PageParam<ColumnInfo, ColumnInfoParam> pageParam = new PageParam<>(param);
        return generatorService.pageColumn(dataSource, tableName, pageParam);
    }
    
    /**
     * 生成代码
     */
    @OperationLog(module = "代码生成", comments = "生成代码")
    @PostMapping("/generate")
    public Object generate(@RequestBody GenConfig config, HttpServletResponse response) {
        try {
            byte[] data = generatorService.generate(config);
            
            // 如果返回zip包
            if (StringUtils.isEmpty(config.getFrontPath()) && StringUtils.isEmpty(config.getBackPath())) {
                response.reset();
                response.setHeader("Content-Disposition", "attachment; filename=\"code.zip\"");
                response.addHeader("Content-Length", "" + data.length);
                response.setContentType("application/octet-stream; charset=UTF-8");
                
                IOUtils.write(data, response.getOutputStream());
                return null;
            }
            
            // 返回成功消息
            return success("代码生成成功");
        } catch (Exception e) {
            return fail("代码生成失败: " + e.getMessage());
        }
    }
} 