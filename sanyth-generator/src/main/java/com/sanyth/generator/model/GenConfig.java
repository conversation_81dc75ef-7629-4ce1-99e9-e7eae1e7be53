package com.sanyth.generator.model;

import lombok.Data;

import java.util.List;

@Data
public class GenConfig {
    /**
     * 作者
     */
    private String author;

    /**
     * 模块名
     */
    private String moduleName;

    /**
     * 包名
     */
    private String packageName;

    /**
     * 前端路径
     */
    private String frontPath;

    /**
     * 后端路径
     */
    private String backPath;

    /**
     * 是否生成后端代码
     */
    private Boolean generateBack;

    /**
     * 是否生成前端代码
     */
    private Boolean generateFront;

    /**
     * 是否生成SQL文件
     */
    private Boolean generateSql;

    /**
     * 数据源配置
     */
    private DataSourceConfig dataSource;

    /**
     * 表名列表
     */
    private List<String> tables;
} 