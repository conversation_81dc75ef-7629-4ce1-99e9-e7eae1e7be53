package com.sanyth.generator.utils;

import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import org.springframework.util.StringUtils;

import java.io.StringWriter;
import java.util.Map;
import java.util.Properties;

/**
 * Velocity模板工具类
 */
public class VelocityUtils {

    /**
     * 初始化Velocity引擎
     */
    static {
        Properties prop = new Properties();
        prop.put("file.resource.loader.class", "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
        Velocity.init(prop);
    }

    /**
     * 渲染模板
     *
     * @param template 模板文件名
     * @param context 模板上下文参数
     * @return 渲染后的内容
     */
    public static String render(String template, Map<String, Object> context) {
        if (!StringUtils.hasText(template)) {
            return "";
        }
        try {
            Template tpl = Velocity.getTemplate("templates/" + template, "UTF-8");
            VelocityContext velocityContext = new VelocityContext(context);
            StringWriter sw = new StringWriter();
            tpl.merge(velocityContext, sw);
            return sw.toString();
        } catch (Exception e) {
            throw new RuntimeException("渲染模板失败:" + template + ", 原因:" + e.getMessage(), e);
        }
    }
}