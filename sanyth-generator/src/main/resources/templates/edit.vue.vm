<template>
  <ele-drawer
    :size="530"
    :title="isUpdate ? '修改' : '新建'"
    :append-to-body="true"
    style="max-width: 100%"
    :destroy-on-close="true"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <pro-form
      ref="formRef"
      :model="form"
      :items="items"
      :grid="{ span: 24 }"
      :labelWidth="110"
      @updateValue="setFieldValue"
    >
    </pro-form>
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="onSubmit">
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import {ref, watch} from 'vue';
  import {useFormData} from '@/utils/use-form-data';
  import {EleMessage} from 'ele-admin-plus';
  import {operation} from '../api';
  import ProForm from '@/components/ProForm/index.vue';

    #if($!{hasDictOptions})
  import { #foreach($dict in $dictOptions)$dict.name#if($foreach.hasNext), #end#end } from '../config';
#end

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    id: void 0,
#set($last = 0)
#foreach($column in $columns)
#if(!$column.isPrimaryKey)
#set($last = $last + 1)
#end
#end
#set($current = 0)
#foreach($column in $columns)
#if(!$column.isPrimaryKey)
#set($current = $current + 1)
#if($current == $last)
    ${column.attrname}: #if($column.attrType == "Integer" || $column.attrType == "Long")void 0#else''#end
#else
    ${column.attrname}: #if($column.attrType == "Integer" || $column.attrType == "Long")void 0#else''#end,
#end
#end
#end
  });

  /** 表单项 */
  const items = ref([
#foreach($column in $columns)
#if(!$column.isPrimaryKey && $column.isList)
    {
      prop: '${column.attrname}',
      label: '${column.columnComment}',
      type: 'input'#if(!$column.isNullable),
      required: true#end
    }#if($foreach.hasNext),#end
#end
#end
  ]);

  /** 提交状态 */
  const loading = ref(false);

  /** 提交 */
  const onSubmit = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      operation(form)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          assignFields({
            ...props.data
          });
          isUpdate.value = true;
        } else {
          resetFields();
          formRef.value?.clearValidate?.();
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>