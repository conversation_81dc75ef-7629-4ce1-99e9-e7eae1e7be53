package com.sanyth.langchain.api.controller;

import com.sanyth.core.annotation.OperationLog;
import com.sanyth.core.web.BaseController;
import com.sanyth.core.web.PageParam;
import com.sanyth.core.web.PageResult;
import com.sanyth.langchain.api.entity.AigcDocsSlice;
import com.sanyth.langchain.api.mapper.AigcDocsSliceMapper;
import com.sanyth.langchain.api.param.AigcDocsSliceParam;
import com.sanyth.langchain.api.service.EmbeddingService;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 文档切片控制器
 *
 * <AUTHOR>
 * @since 2025-02-28 11:08:42
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/aigc/AigcDocsSlice")
public class AigcDocsSliceController extends BaseController {

    private final AigcDocsSliceMapper docsSliceMapper;
    private final EmbeddingService embeddingService;
    /**
     * 分页查询文档切片（权限标识：aigc:AigcDocsSlice:list）
     */
//    @PreAuthorize("hasAuthority('aigc:AigcDocsSlice:list')")
    @GetMapping("/page")
    public PageResult<AigcDocsSlice> page(AigcDocsSliceParam param) {
        PageParam<AigcDocsSlice, AigcDocsSliceParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = docsSliceMapper.selectPage(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部文档切片（权限标识：aigc:AigcDocsSlice:list）
     */
//    @PreAuthorize("hasAuthority('aigc:AigcDocsSlice:list')")
    @GetMapping()
    public List<AigcDocsSlice> list(AigcDocsSliceParam param) {
        PageParam<AigcDocsSlice, AigcDocsSliceParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return docsSliceMapper.selectList(page.getOrderWrapper());
    }

    /**
     * 根据id查询文档切片（权限标识：aigc:AigcDocsSlice:list）
     */
//    @PreAuthorize("hasAuthority('aigc:AigcDocsSlice:list')")
    @GetMapping("/{id}")
    public AigcDocsSlice get(@PathVariable("id") String id) {
        return docsSliceMapper.selectById(id);
    }

    /**
     * 添加或修改文档切片（权限标识：aigc:AigcDocsSlice:operation）
     */
//    @PreAuthorize("hasAuthority('aigc:AigcDocsSlice:operation')")
    @OperationLog(module = "文档切片", comments = "保存文档切片")
    @PostMapping("/operation")
    public void save(@RequestBody AigcDocsSlice aigcDocsSlice) {
        if (StringUtils.hasLength(aigcDocsSlice.getId())) {
            docsSliceMapper.updateById(aigcDocsSlice);
        } else {
            docsSliceMapper.insert(aigcDocsSlice);
        }
    }

    /**
     * 批量删除文档切片（权限标识：aigc:AigcDocsSlice:remove）
     */
//    @PreAuthorize("hasAuthority('aigc:AigcDocsSlice:remove')")
    @OperationLog(module = "文档切片", comments = "批量删除文档切片")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        ids.forEach(embeddingService::clearDocSliceById);
        docsSliceMapper.deleteBatchIds(ids);
    }
} 