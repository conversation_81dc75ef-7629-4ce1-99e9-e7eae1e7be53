package com.sanyth.langchain.api.controller;

import com.sanyth.core.annotation.EnableMask;
import com.sanyth.core.annotation.OperationLog;
import com.sanyth.core.enums.State;
import com.sanyth.core.utils.SpringContextHolder;
import com.sanyth.core.web.BaseController;
import com.sanyth.core.web.PageParam;
import com.sanyth.core.web.PageResult;
import com.sanyth.langchain.api.entity.AigcModel;
import com.sanyth.langchain.api.param.AigcModelParam;
import com.sanyth.langchain.api.service.AigcModelService;
import com.sanyth.langchain.core.config.ProviderRefreshEvent;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * LLM模型配置表控制器
 *
 * <AUTHOR>
 * @since 2024-08-29 15:21:17
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/aigc/aigc-model")
public class AigcModelController extends BaseController {
    private final AigcModelService aigcModelService;
    private final SpringContextHolder contextHolder;

    /**
     * 分页查询LLM模型配置表（权限标识：aigc:aigcModel:list）
     */
    @EnableMask
//    @PreAuthorize("hasAuthority('aigc:aigcModel:list')")
    @GetMapping("/page")
    public PageResult<AigcModel> page(AigcModelParam param) {
        PageParam<AigcModel, AigcModelParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = aigcModelService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部LLM模型配置表（权限标识：aigc:aigcModel:list）
     */
//    @EnableMask
//    @PreAuthorize("hasAuthority('aigc:aigcModel:list')")
    @GetMapping()
    public List<AigcModel> list(AigcModelParam param) {
        param.setStatus(State.enabled);
        PageParam<AigcModel, AigcModelParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return aigcModelService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询LLM模型配置表（权限标识：aigc:aigcModel:list）
     */
//    @PreAuthorize("hasAuthority('aigc:aigcModel:list')")
    @GetMapping("/{id}")
    public AigcModel get(@PathVariable("id") String id) {
        return aigcModelService.getById(id);
    }

    /**
     * 添加或修改LLM模型配置表（权限标识：message:aigcModel:operation）
     */
//    @PreAuthorize("hasAuthority('aigc:aigcModel:operation')")
    @OperationLog(module = "LLM模型配置表", comments = "保存LLM模型配置表")
    @PostMapping("/operation")
    public void save(@RequestBody AigcModel aigcModel) {
        if (StringUtils.hasLength(aigcModel.getId())) {
            aigcModelService.updateById(aigcModel);
        } else {
            aigcModelService.save(aigcModel);
        }
        //刷新模型
        SpringContextHolder.publishEvent(new ProviderRefreshEvent(aigcModel));
    }

    /**
     * 批量删除LLM模型配置表（权限标识：aigc:aigcModel:remove）
     */
//    @PreAuthorize("hasAuthority('aigc:aigcModel:remove')")
    @OperationLog(module = "LLM模型配置表", comments = "批量删除LLM模型配置表")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        aigcModelService.removeByIds(ids);
        for (String id : ids) {
            contextHolder.unregisterBean(id);
        }
    }
}
