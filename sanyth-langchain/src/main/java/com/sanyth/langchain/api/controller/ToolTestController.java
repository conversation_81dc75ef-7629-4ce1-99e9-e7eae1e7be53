package com.sanyth.langchain.api.controller;

import com.alibaba.fastjson.JSONObject;
import com.sanyth.core.web.ApiResult;
import com.sanyth.core.web.BaseController;
import com.sanyth.langchain.api.entity.AigcTool;
import com.sanyth.langchain.api.service.AigcToolService;
import com.sanyth.langchain.api.service.impl.ToolExecutorService;
import io.netty.buffer.PoolArenaMetric;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import static com.sanyth.upms.utils.SecurityUtil.getAccount;

/**
 * 工具测试控制器
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/aigc/tool/test")
public class ToolTestController extends BaseController {

    private final ToolExecutorService toolExecutorService;
    private final AigcToolService aigcToolService;
    
    /**
     * 测试工具执行
     */
    @PostMapping("/{id}")
    public ApiResult<?> testTool(@PathVariable("id") String id, @RequestBody JSONObject body) {
        AigcTool tool = aigcToolService.getById(id);
        JSONObject params = new JSONObject();
        //动态参数{} 需格式化的参数
        params.put("arguments", body);
        params.put("user", getAccount());
        //工具参数
        params.put("toolParams", tool.getParams());
        String result = toolExecutorService.executeToolByParams(params);
        return success(result);
    }
}