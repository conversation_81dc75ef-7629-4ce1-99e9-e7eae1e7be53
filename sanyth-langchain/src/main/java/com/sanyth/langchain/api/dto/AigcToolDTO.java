package com.sanyth.langchain.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * AI工具DTO
 */
@Data
public class AigcToolDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 工具ID
     */
    private String id;

    /**
     * 工具名称
     */
    private String name;

    /**
     * 工具描述
     */
    private String desc;

    /**
     * 工具类型(SYSTEM:系统内置,CUSTOM:自定义)
     */
    private String type;

    /**
     * 状态(0:启用,1:禁用)
     */
    private String status;

    /**
     * 工具参数配置(JSON格式)
     */
    private String params;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 