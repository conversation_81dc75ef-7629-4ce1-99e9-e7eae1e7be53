package com.sanyth.langchain.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 应用API
 *
 * <AUTHOR>
 * @since 2025-02-25 16:25:27
 */

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_AIGC_APP_API")
@Entity
@Table(name = "SYT_AIGC_APP_API")
public class AigcAppApi implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = "VARCHAR2(32)")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 应用ID
     */
    @Column(name = "APP_ID")
    @TableField("APP_ID")
    private String appId;

    /**
     * API_KEY
     */
    @Column(name = "API_KEY")
    @TableField("API_KEY")
    private String apiKey;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @TableField("CREATE_TIME")
    private Date createTime;

    /**
     * 频道
     */
    @Column(name = "CHANNEL")
    @TableField("CHANNEL")
    private String channel;

    @Transient
    @TableField(exist = false)
    private AigcApp app;

} 