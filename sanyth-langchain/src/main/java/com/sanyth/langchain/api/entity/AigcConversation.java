package com.sanyth.langchain.api.entity;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

/**
 * 对话列表
 *
 * <AUTHOR>
 * @date 2025/02/19
 */
@Data
@Accessors(chain = true)
@Document(collection = "SYT_AIGC_CONVERSATION")
public class AigcConversation implements Serializable {

    private static final long serialVersionUID = -19545329638997333L;

    /**
     * 主键
     */
    private String id;

    /**
     * 提示词ID
     */
    private String promptId;

    /**
     * 用户ID
     */
    @Indexed
    private String userId;

    /**
     * 应用ID
     */
    @Indexed
    private String appId;

    /**
     * 会话标题
     */
    private String title;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 用户名
     */
    private String username;

    /**
     * 对话次数
     */
    private Integer chatTotal;
    /**
     * Token消耗量
     */
    private Integer tokenUsed;
    /**
     * 最后一次对话时间
     */
    private Date endTime;

    /**
     * 会话状态：0-进行中，1-正常结束，2-异常结束
     */
    private Integer status;
}