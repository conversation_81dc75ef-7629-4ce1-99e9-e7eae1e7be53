package com.sanyth.langchain.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanyth.core.enums.State;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * AI工具实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_AIGC_TOOL")
@Entity
@Table(name = "SYT_AIGC_TOOL")
public class AigcTool implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 工具ID
     */
    @Id
    @Column(name = "ID", columnDefinition = "VARCHAR2(32)")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 工具名称
     */
    @TableField("name")
    private String name;

    /**
     * 工具描述
     */
    @Column(name = "DESCRIPTION", columnDefinition = "VARCHAR2(4000)")
    @TableField("DESCRIPTION")
    private String description;

    /**
     * 工具类型(SYSTEM:系统内置,CUSTOM:自定义)
     */
    @TableField("type")
    private String type;

    /**
     * 状态(0:启用,1:禁用)
     */
    @Column(name = "STATUS", columnDefinition = "NUMBER(1)")
    @TableField("status")
    private State status;

    /**
     * 工具参数配置(JSON格式)
     */
    @Column(name = "PARAMS", columnDefinition = "CLOB")
    @TableField("params")
    private String params;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建者
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新者
     */
    @TableField("update_by")
    private String updateBy;
} 