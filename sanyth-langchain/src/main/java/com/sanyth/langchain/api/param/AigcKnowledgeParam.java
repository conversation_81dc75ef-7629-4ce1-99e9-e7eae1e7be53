package com.sanyth.langchain.api.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.sanyth.core.web.BaseParam;
import com.sanyth.langchain.api.entity.AigcDocs;
import com.sanyth.langchain.api.entity.AigcEmbedStore;
import com.sanyth.langchain.api.entity.AigcModel;
import jakarta.persistence.Column;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 知识库 参数
 *
 * <AUTHOR>
 * @since 2025-02-25 17:43:11
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AigcKnowledgeParam extends BaseParam {

    /**
     * 
     */
    private String id;

    /**
     * 封面
     */
    private String cover;

    /**
     * 内嵌模型ID
     */
    private String embedModelId;

    /**
     * 描述
     */
    private String des;

    /**
     * 名称
     */
    private String name;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 内嵌存储ID
     */
    private String embedStoreId;

    /**
     * 文档数
     */
    @TableField(exist = false)
    private Integer docsNum = 0;
    /**
     * 大小
     */
    @TableField(exist = false)
    private Long totalSize = 0L;

    /**
     * 文档列表
     */
    @TableField(exist = false)
    private List<AigcDocs> docs = new ArrayList<>();

    /**
     * 向量数据库
     */
    @TableField(exist = false)
    private AigcEmbedStore embedStore;

    /**
     * 向量模型
     */
    @TableField(exist = false)
    private AigcModel embedModel;

    /**
     * 最大的分段大小
     */
    private Integer maxSegmentSize;
    /**
     * 最大重叠大小
     */
    private Integer maxOverlapSize;
} 