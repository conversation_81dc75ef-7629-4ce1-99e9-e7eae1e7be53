package com.sanyth.langchain.api.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanyth.core.annotation.QueryField;
import com.sanyth.core.annotation.QueryType;
import com.sanyth.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AigcMessageParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    @QueryField(type = QueryType.EQ)
    private String userId;

    @QueryField(type = QueryType.EQ)
    private String conversationId;

    @QueryField(type = QueryType.LIKE)
    private String content;

    @QueryField(type = QueryType.EQ)
    private Integer type;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "create_time desc";
    }
} 