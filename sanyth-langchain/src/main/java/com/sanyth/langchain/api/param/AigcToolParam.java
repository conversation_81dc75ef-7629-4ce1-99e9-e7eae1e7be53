package com.sanyth.langchain.api.param;

import com.baomidou.mybatisplus.annotation.TableField;
import com.sanyth.core.annotation.QueryField;
import com.sanyth.core.annotation.QueryType;
import com.sanyth.core.enums.State;
import com.sanyth.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * AI工具查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AigcToolParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 工具ID
     */
    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 工具名称
     */
    @QueryField(type = QueryType.LIKE)
    private String name;

    /**
     * 工具类型
     */
    @QueryField(type = QueryType.EQ)
    private String type;

    /**
     * 状态
     */
    @QueryField(type = QueryType.EQ)
    private State status;

    /**
     * 工具描述
     */
    private String description;
    
    @Override
    public String getSort() {
        return "create_time desc";
    }
} 