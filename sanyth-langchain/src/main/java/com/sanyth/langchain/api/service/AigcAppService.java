package com.sanyth.langchain.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.core.web.PageParam;
import com.sanyth.core.web.PageResult;
import com.sanyth.langchain.api.entity.AigcApp;
import com.sanyth.langchain.api.param.AigcAppParam;
import com.sanyth.langchain.api.vo.AigcAppVO;

import java.util.List;

/**
 * AIGC应用 Service
 *
 * <AUTHOR>
 * @since 2025-02-20 23:58:24
 */
public interface AigcAppService extends IService<AigcApp> {

    /**
     * 分页查询
     */
    PageResult<AigcApp> page(PageParam<AigcApp, AigcAppParam> page);

    /**
     * 查询全部
     */
    List<AigcApp> list(AigcAppParam param);

    /**
     * 添加
     */
    boolean add(AigcApp entity);

    /**
     * 修改
     */
    boolean update(AigcApp entity);

    /**
     * 删除
     */
    boolean delete(List<String> ids);

    void saveAigcApp(AigcAppParam param) throws Exception;

    PageResult<AigcApp> queryPage(AigcAppParam param);

    AigcAppVO getAigcAppVO(String id);
}