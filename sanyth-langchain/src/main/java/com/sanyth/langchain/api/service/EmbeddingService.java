package com.sanyth.langchain.api.service;


import com.sanyth.langchain.api.entity.AigcDocs;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface EmbeddingService {

    void clearDocSlices(String docsId);

    void clearDocSliceById(String sliceId);

    void embedDocsSliceByUrl(AigcDocs data, String url);

    void embedDocsSlice(AigcDocs data, String fileId);

    List<Map<String, Object>> search(AigcDocs data);
}
