package com.sanyth.langchain.api.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.langchain.api.entity.AigcAppApi;
import com.sanyth.langchain.api.mapper.AigcAppApiMapper;
import com.sanyth.langchain.api.service.AigcAppApiService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 应用API Service实现
 *
 * <AUTHOR>
 * @since 2025-02-25 16:25:27
 */
@RequiredArgsConstructor
@Service
public class AigcAppApiServiceImpl extends ServiceImpl<AigcAppApiMapper, AigcAppApi> implements AigcAppApiService {

    private final AigcAppApiMapper mapper;

} 