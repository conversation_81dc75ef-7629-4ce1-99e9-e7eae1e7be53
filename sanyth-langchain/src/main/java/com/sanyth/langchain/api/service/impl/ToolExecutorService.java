package com.sanyth.langchain.api.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.sanyth.core.exception.BusinessException;
import com.sanyth.langchain.api.service.AigcToolService;
import com.sanyth.langchain.api.service.ToolTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 工具执行服务
 * 负责解析工具配置并调用相应的工具模板执行
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ToolExecutorService {
    
    private final AigcToolService aigcToolService;
    private final ApplicationContext applicationContext;
    
    // 工具类型与工具模板Bean名称的映射
    private static final Map<String, String> TOOL_TYPE_MAPPING = new HashMap<>();
    
    static {
        // 注册工具类型与对应的工具模板Bean名称映射
        TOOL_TYPE_MAPPING.put("http", "httpToolTemplate");
        // 可以添加更多工具类型
    }
    
    /**
     * 根据工具ID执行工具
     * @return 执行结果
     */
    public String executeToolByParams(JSONObject params) throws BusinessException {
        JSONObject toolParams = params.getJSONObject("toolParams");
        String toolType = toolParams.getString("type");
        
        if (!StringUtils.hasText(toolType) || !TOOL_TYPE_MAPPING.containsKey(toolType)) {
            throw new BusinessException("不支持的工具类型: " + toolType);
        }
        // 获取工具模板
        String templateBeanName = TOOL_TYPE_MAPPING.get(toolType);
        ToolTemplate toolTemplate = applicationContext.getBean(templateBeanName, ToolTemplate.class);
        try {
            return toolTemplate.execute(params);
        } catch (Exception e) {
            log.error("工具执行异常", e);
            throw new BusinessException("工具执行失败: " + e.getMessage());
        }
    }
}