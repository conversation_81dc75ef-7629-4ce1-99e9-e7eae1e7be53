package com.sanyth.langchain.api.store;

import com.sanyth.langchain.api.entity.AigcApp;
import com.sanyth.langchain.api.service.AigcAppService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
@AllArgsConstructor
public class AppStore {

    private final Map<String, AigcApp> appMap = new ConcurrentHashMap<>();
    @Resource
    @Lazy
    private AigcAppService aigcAppService;

    @PostConstruct
    public void init() {
        log.info("Initializing app config list...");
        try {
            List<AigcApp> apps = aigcAppService.list();
            appMap.clear();
            apps.forEach(app -> appMap.put(app.getId(), app));
            log.info("Successfully initialized {} apps", appMap.size());
        } catch (Exception e) {
            log.error("Failed to initialize app config list", e);
            throw new RuntimeException("App store initialization failed", e);
        }
    }

    public AigcApp get(String appId) {
        if (appId == null) {
            throw new IllegalArgumentException("App ID cannot be null");
        }
        return appMap.get(appId);
    }

    public Map<String, AigcApp> getAllApps() {
        return Collections.unmodifiableMap(appMap);
    }

    public void refresh() {
        init();
    }
}
