package com.sanyth.langchain.biz.service.speech.impl;

import com.sanyth.langchain.biz.service.speech.SpeechRecognizer;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 阿里云语音识别实现
 *
 * <AUTHOR>
 * @date 2024/09/05
 */
@Slf4j
@Component
public class AliyunSpeechRecognizer implements SpeechRecognizer {

    @Value("${speech.aliyun.access-key-id:}")
    private String accessKeyId;

    @Value("${speech.aliyun.access-key-secret:}")
    private String accessKeySecret;

    @Value("${speech.aliyun.app-key:}")
    private String appKey;
    
    @Value("${speech.aliyun.timeout-connection:10}")
    private int connectionTimeout;
    
    @Value("${speech.aliyun.timeout-read:60}")
    private int readTimeout;
    
    // 阿里云语音识别API地址
    private static final String API_URL = "https://nls-gateway.cn-shanghai.aliyuncs.com/stream/v1/asr";

    @Override
    public String recognize(MultipartFile audio) throws Exception {
        // 检查配置
        if (accessKeyId.isEmpty() || accessKeySecret.isEmpty() || appKey.isEmpty()) {
            log.warn("阿里云语音识别配置不完整");
            return "语音识别服务未配置，请联系管理员";
        }

        // 创建临时文件
        File tempFile = createTempFile(audio);
        try {
            // 读取音频文件
            byte[] audioData = Files.readAllBytes(tempFile.toPath());
            
            // 获取音频格式
            String audioFormat = getAudioFormat(tempFile.getName());
            
            // 构建请求URL
            String requestUrl = buildRequestUrl(audioFormat);
            
            log.info("开始调用阿里云语音识别API，文件大小: {}字节", audioData.length);
            
            // 发送请求
            String result = sendRequest(requestUrl, audioData);
            
            log.info("阿里云语音识别返回结果: {}", result);
            
            // 解析结果
            return parseResult(result);
        } catch (Exception e) {
            log.error("阿里云语音识别异常", e);
            throw new Exception("阿里云语音识别处理失败: " + e.getMessage());
        } finally {
            // 删除临时文件
            if (tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    /**
     * 构建请求URL
     *
     * @param audioFormat 音频格式
     * @return 请求URL
     * @throws Exception 异常
     */
    private String buildRequestUrl(String audioFormat) throws Exception {
        // 构建请求参数
        Map<String, String> params = new HashMap<>();
        params.put("appkey", appKey);
        
        // 设置音频格式
        String format = "pcm";
        if ("wav".equals(audioFormat)) {
            format = "wav";
        } else if ("mp3".equals(audioFormat)) {
            format = "mp3";
        }
        params.put("format", format);
        
        // 设置采样率
        params.put("sample_rate", "16000");
        
        // 设置语言
        params.put("enable_punctuation_prediction", "true");
        params.put("enable_inverse_text_normalization", "true");
        
        // 构建URL
        StringBuilder urlBuilder = new StringBuilder(API_URL);
        urlBuilder.append("?");
        
        // 添加参数
        for (Map.Entry<String, String> entry : params.entrySet()) {
            urlBuilder.append(URLEncoder.encode(entry.getKey(), "UTF-8"))
                    .append("=")
                    .append(URLEncoder.encode(entry.getValue(), "UTF-8"))
                    .append("&");
        }
        
        // 移除最后的&
        if (urlBuilder.charAt(urlBuilder.length() - 1) == '&') {
            urlBuilder.deleteCharAt(urlBuilder.length() - 1);
        }
        
        // 添加签名
        String url = urlBuilder.toString();
        String signature = generateSignature(url);
        
        // 返回带签名的URL
        return url + "&signature=" + URLEncoder.encode(signature, "UTF-8");
    }

    /**
     * 生成签名
     *
     * @param url 请求URL
     * @return 签名
     * @throws NoSuchAlgorithmException 算法异常
     * @throws InvalidKeyException 密钥异常
     */
    private String generateSignature(String url) throws NoSuchAlgorithmException, InvalidKeyException {
        // 获取当前时间戳
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        String timestamp = sdf.format(new Date());
        
        // 构建签名字符串
        String stringToSign = "POST\n" +
                "application/octet-stream\n" +
                "\n" +
                "x-nls-timestamp:" + timestamp + "\n" +
                url.substring(url.indexOf("/stream"));
        
        // 计算签名
        Mac mac = Mac.getInstance("HmacSHA1");
        mac.init(new SecretKeySpec(accessKeySecret.getBytes(StandardCharsets.UTF_8), "HmacSHA1"));
        byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
        
        // Base64编码
        return Base64.getEncoder().encodeToString(signData);
    }

    /**
     * 发送请求
     *
     * @param url 请求URL
     * @param audioData 音频数据
     * @return 响应结果
     * @throws IOException IO异常
     */
    private String sendRequest(String url, byte[] audioData) throws IOException {
        // 获取当前时间戳
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT"));
        String timestamp = sdf.format(new Date());
        
        // 创建OkHttpClient
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(connectionTimeout, TimeUnit.SECONDS)
                .readTimeout(readTimeout, TimeUnit.SECONDS)
                .build();
        
        // 构建请求体
        RequestBody requestBody = RequestBody.create(MediaType.parse("application/octet-stream"), audioData);
        
        // 构建请求
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/octet-stream")
                .addHeader("Accept", "application/json")
                .addHeader("x-nls-timestamp", timestamp)
                .addHeader("x-nls-access-key-id", accessKeyId)
                .post(requestBody)
                .build();
        
        // 发送请求
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("请求失败: " + response);
            }
            return response.body().string();
        }
    }

    /**
     * 解析结果
     *
     * @param result 响应结果
     * @return 识别文本
     */
    private String parseResult(String result) {
        try {
            JSONObject jsonResult = new JSONObject(result);
            
            // 检查是否成功
            if (jsonResult.has("status") && jsonResult.getInt("status") != 20000000) {
                int errorCode = jsonResult.getInt("status");
                String errorMessage = jsonResult.optString("message", "未知错误");
                
                log.error("阿里云语音识别失败: 错误码={}, 错误信息={}", errorCode, errorMessage);
                
                // 处理特定错误
                if (errorCode == 20400002) {
                    return "未检测到有效语音，请重新录音";
                } else if (errorCode == 20400003) {
                    return "音频格式不支持，请使用支持的格式";
                } else if (errorCode == 20400004) {
                    return "音频采样率不支持，请使用16k采样率";
                } else if (errorCode == 20400005) {
                    return "音频数据过大，请缩短录音时间";
                } else {
                    return "语音识别失败，错误码: " + errorCode + ", " + errorMessage;
                }
            }
            
            // 解析识别结果
            if (jsonResult.has("result")) {
                String recognizedText = jsonResult.getString("result");
                log.info("阿里云语音识别成功: {}", recognizedText);
                return recognizedText;
            }
            
            // 如果有详细结果
            if (jsonResult.has("flash_result") && jsonResult.getJSONObject("flash_result").has("sentences")) {
                JSONArray sentences = jsonResult.getJSONObject("flash_result").getJSONArray("sentences");
                
                StringBuilder text = new StringBuilder();
                for (int i = 0; i < sentences.length(); i++) {
                    JSONObject sentence = sentences.getJSONObject(i);
                    if (sentence.has("text")) {
                        text.append(sentence.getString("text"));
                    }
                }
                
                String recognizedText = text.toString();
                log.info("阿里云语音识别成功: {}", recognizedText);
                return recognizedText;
            }
            
            return "语音识别未返回有效结果，请重试";
        } catch (Exception e) {
            log.error("解析阿里云语音识别结果异常", e);
            return "解析语音识别结果失败: " + e.getMessage();
        }
    }

    /**
     * 创建临时文件
     *
     * @param audio 音频文件
     * @return 临时文件
     * @throws IOException IO异常
     */
    private File createTempFile(MultipartFile audio) throws IOException {
        String originalFilename = audio.getOriginalFilename();
        String suffix = originalFilename != null && originalFilename.contains(".") 
                ? originalFilename.substring(originalFilename.lastIndexOf(".")) 
                : ".wav";
        
        // 创建临时目录
        Path tempDir = Files.createTempDirectory("speech_");
        File tempFile = new File(tempDir.toFile(), UUID.randomUUID() + suffix);
        
        // 写入文件内容
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            fos.write(audio.getBytes());
        }
        
        return tempFile;
    }
    
    /**
     * 根据文件名获取音频格式
     *
     * @param filename 文件名
     * @return 音频格式
     */
    private String getAudioFormat(String filename) {
        if (filename.toLowerCase().endsWith(".wav")) {
            return "wav";
        } else if (filename.toLowerCase().endsWith(".mp3")) {
            return "mp3";
        } else if (filename.toLowerCase().endsWith(".pcm")) {
            return "pcm";
        } else if (filename.toLowerCase().endsWith(".m4a")) {
            return "m4a";
        } else {
            // 默认格式
            return "wav";
        }
    }
} 