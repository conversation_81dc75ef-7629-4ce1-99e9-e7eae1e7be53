package com.sanyth.langchain.biz.service.speech.impl;

import com.baidu.aip.speech.AipSpeech;
import com.sanyth.langchain.biz.service.speech.SpeechRecognizer;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.UUID;

/**
 * 百度语音识别实现
 *
 * <AUTHOR>
 * @date 2024/09/05
 */
@Slf4j
@Component
public class BaiduSpeechRecognizer implements SpeechRecognizer {

    @Value("${speech.baidu.app-id:}")
    private String appId;

    @Value("${speech.baidu.api-key:}")
    private String apiKey;

    @Value("${speech.baidu.secret-key:}")
    private String secretKey;
    
    @Value("${speech.baidu.timeout-connection:2000}")
    private int connectionTimeout;
    
    @Value("${speech.baidu.timeout-socket:60000}")
    private int socketTimeout;

    @Override
    public String recognize(MultipartFile audio) throws Exception {
        // 检查配置
        if (appId.isEmpty() || apiKey.isEmpty() || secretKey.isEmpty()) {
            log.warn("百度语音识别配置不完整");
            return "语音识别服务未配置，请联系管理员";
        }

        // 创建临时文件
        File tempFile = createTempFile(audio);
        try {
            // 初始化百度语音客户端
            AipSpeech client = new AipSpeech(appId, apiKey, secretKey);
            
            // 设置超时时间
            client.setConnectionTimeoutInMillis(connectionTimeout);
            client.setSocketTimeoutInMillis(socketTimeout);
            
            // 读取音频文件
            byte[] data = Files.readAllBytes(tempFile.toPath());
            
            // 设置可选参数
            HashMap<String, Object> options = new HashMap<>();
            options.put("dev_pid", 1537); // 普通话(支持简单的英文识别)
            options.put("format", getAudioFormat(tempFile.getName()));
            options.put("rate", 16000); // 采样率
            options.put("channel", 1); // 声道数
            
            log.info("开始调用百度语音识别API，文件大小: {}字节", data.length);
            
            // 调用百度语音识别API
            JSONObject result = client.asr(data, getAudioFormat(tempFile.getName()), 16000, options);
            
            log.info("百度语音识别返回结果: {}", result.toString());
            
            // 解析结果
            if (result.has("result")) {
                JSONArray resultArray = result.getJSONArray("result");
                if (resultArray.length() > 0) {
                    String recognizedText = resultArray.getString(0);
                    log.info("百度语音识别成功: {}", recognizedText);
                    return recognizedText;
                }
            }
            
            // 处理错误
            if (result.has("err_no") && result.getInt("err_no") != 0) {
                int errorCode = result.getInt("err_no");
                String errorMsg = result.optString("err_msg", "未知错误");
                
                log.error("百度语音识别失败: 错误码={}, 错误信息={}", errorCode, errorMsg);
                
                // 处理特定错误
                if (errorCode == 3301) {
                    return "未检测到有效语音，请重新录音";
                } else if (errorCode == 3302) {
                    return "语音识别服务配置错误，请联系管理员";
                } else if (errorCode == 3303) {
                    return "音频格式不支持，请使用支持的格式";
                } else if (errorCode == 3304) {
                    return "音频采样率不支持，请使用16k采样率";
                } else if (errorCode == 3305) {
                    return "音频数据过大，请缩短录音时间";
                } else {
                    return "语音识别失败，错误码: " + errorCode + ", " + errorMsg;
                }
            }
            
            return "语音识别未返回有效结果，请重试";
        } catch (Exception e) {
            log.error("百度语音识别异常", e);
            throw new Exception("百度语音识别处理失败: " + e.getMessage());
        } finally {
            // 删除临时文件
            if (tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    /**
     * 创建临时文件
     *
     * @param audio 音频文件
     * @return 临时文件
     * @throws IOException IO异常
     */
    private File createTempFile(MultipartFile audio) throws IOException {
        String originalFilename = audio.getOriginalFilename();
        String suffix = originalFilename != null && originalFilename.contains(".") 
                ? originalFilename.substring(originalFilename.lastIndexOf(".")) 
                : ".wav";
        
        // 创建临时目录
        Path tempDir = Files.createTempDirectory("speech_");
        File tempFile = new File(tempDir.toFile(), UUID.randomUUID() + suffix);
        
        // 写入文件内容
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            fos.write(audio.getBytes());
        }
        
        return tempFile;
    }
    
    /**
     * 根据文件名获取音频格式
     *
     * @param filename 文件名
     * @return 音频格式
     */
    private String getAudioFormat(String filename) {
        if (filename.toLowerCase().endsWith(".wav")) {
            return "wav";
        } else if (filename.toLowerCase().endsWith(".mp3")) {
            return "mp3";
        } else if (filename.toLowerCase().endsWith(".pcm")) {
            return "pcm";
        } else if (filename.toLowerCase().endsWith(".m4a")) {
            return "m4a";
        } else {
            // 默认格式
            return "pcm";
        }
    }
} 