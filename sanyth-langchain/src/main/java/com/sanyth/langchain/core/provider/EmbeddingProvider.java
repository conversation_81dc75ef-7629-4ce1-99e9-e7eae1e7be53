package com.sanyth.langchain.core.provider;

import com.sanyth.core.exception.BusinessException;
import com.sanyth.langchain.api.entity.AigcKnowledge;
import dev.langchain4j.data.document.DocumentSplitter;
import dev.langchain4j.data.document.splitter.DocumentSplitters;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.Tokenizer;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.openai.OpenAiChatModelName;
import dev.langchain4j.model.openai.OpenAiTokenizer;
import dev.langchain4j.store.embedding.EmbeddingStore;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.context.annotation.Lazy;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

@Slf4j
@Component
@AllArgsConstructor
public class EmbeddingProvider{

    private final EmbeddingStoreFactory embeddingStoreFactory;
    private final KnowledgeStoreFactory knowledgeStoreFactory;
    private final ModelStoreFactory modelStoreFactory;

    public static DocumentSplitter splitter(int maxSegmentSizeInTokens, int maxOverlapSizeInTokens) {
        maxSegmentSizeInTokens = maxSegmentSizeInTokens == 0 ? 500 : maxSegmentSizeInTokens;
        maxOverlapSizeInTokens = maxOverlapSizeInTokens == 0 ? 50 : maxOverlapSizeInTokens;
        return DocumentSplitters.recursive(maxSegmentSizeInTokens, maxOverlapSizeInTokens);
    }

    /**
     * maxSegmentSizeInTokens —— 最大的分段大小（单位是 Token）。
     * 用于控制每个拆分出来的段落、行、句子或单词块的最大 Token 数量。
     * maxOverlapSizeInTokens —— 最大重叠大小（单位是 Token）。
     * 在分割文档时，允许相邻的段落、行、句子或单词之间有一定的重叠部分，提高上下文的连续性。
     * tokenizer —— 分词器（Tokenizer）。
     * 负责将文本分解成 Token，用于计算 maxSegmentSizeInTokens 和 maxOverlapSizeInTokens 的大小。
     *
     * @param maxSegmentSizeInTokens 令牌中最大分段大小
     * @param maxOverlapSizeInTokens 令牌中最大重叠大小
     * @param tokenizer              标记器
     * @return {@link DocumentSplitter }
     */
    public static DocumentSplitter splitter(int maxSegmentSizeInTokens, int maxOverlapSizeInTokens, Tokenizer tokenizer) {
        return DocumentSplitters.recursive(maxSegmentSizeInTokens, maxOverlapSizeInTokens,tokenizer);
    }

    public EmbeddingModel getEmbeddingModel(List<String> knowledgeIds) {
        List<String> storeIds = new ArrayList<>();
        knowledgeIds.forEach(id -> {
            if (knowledgeStoreFactory.containsKnowledge(id)) {
                AigcKnowledge data = knowledgeStoreFactory.getKnowledge(id);
                if (data.getEmbedModelId() != null) {
                    storeIds.add(data.getEmbedModelId());
                }
            }
        });
        if (storeIds.isEmpty()) {
            throw new BusinessException("知识库缺少Embedding Model配置，请先检查配置");
        }

        HashSet<String> filterIds = new HashSet<>(storeIds);
        if (filterIds.size() > 1) {
            throw new BusinessException("存在多个不同Embedding Model的知识库，请先检查配置");
        }

        return modelStoreFactory.getEmbeddingModel(storeIds.get(0));
    }

    public EmbeddingModel getEmbeddingModel(String knowledgeId) {
        if (knowledgeStoreFactory.containsKnowledge(knowledgeId)) {
            AigcKnowledge data = knowledgeStoreFactory.getKnowledge(knowledgeId);
            if (modelStoreFactory.containsEmbeddingModel(data.getEmbedModelId())) {
                return modelStoreFactory.getEmbeddingModel(data.getEmbedModelId());
            }
        }
        throw new BusinessException("没有找到匹配的Embedding向量数据库");
    }

    public EmbeddingStore<TextSegment> getEmbeddingStore(String knowledgeId) {
        if (knowledgeStoreFactory.containsKnowledge(knowledgeId)) {
            AigcKnowledge data = knowledgeStoreFactory.getKnowledge(knowledgeId);
            if (embeddingStoreFactory.containsEmbeddingStore(data.getEmbedStoreId())) {
                return embeddingStoreFactory.getEmbeddingStore(data.getEmbedStoreId());
            }
        }
        throw new BusinessException("没有找到匹配的Embedding向量数据库");
    }

    public EmbeddingStore<TextSegment> getEmbeddingStore(List<String> knowledgeIds) {
        List<String> storeIds = new ArrayList<>();
        knowledgeIds.forEach(id -> {
            if (knowledgeStoreFactory.containsKnowledge(id)) {
                AigcKnowledge data = knowledgeStoreFactory.getKnowledge(id);
                if (data.getEmbedStoreId() != null) {
                    storeIds.add(data.getEmbedStoreId());
                }
            }
        });
        if (storeIds.isEmpty()) {
            throw new BusinessException("知识库缺少Embedding Store配置，请先检查配置");
        }

        HashSet<String> filterIds = new HashSet<>(storeIds);
        if (filterIds.size() > 1) {
            throw new BusinessException("存在多个不同Embedding Store数据源的知识库，请先检查配置");
        }

        return embeddingStoreFactory.getEmbeddingStore(storeIds.get(0));
    }
}
