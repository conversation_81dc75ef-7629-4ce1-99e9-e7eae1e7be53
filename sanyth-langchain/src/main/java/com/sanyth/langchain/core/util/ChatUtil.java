package com.sanyth.langchain.core.util;

import cn.hutool.extra.spring.SpringUtil;
import com.sanyth.langchain.biz.dto.ChatReq;
import com.sanyth.langchain.biz.service.Agent;
import com.sanyth.langchain.core.provider.EmbeddingProvider;
import dev.langchain4j.rag.DefaultRetrievalAugmentor;
import dev.langchain4j.rag.content.retriever.ContentRetriever;
import dev.langchain4j.rag.content.retriever.EmbeddingStoreContentRetriever;
import dev.langchain4j.rag.query.Query;
import dev.langchain4j.service.AiServices;
import dev.langchain4j.store.embedding.filter.Filter;

import java.util.List;
import java.util.function.Function;

import static com.sanyth.langchain.core.constant.EmbedConst.KNOWLEDGE;
import static dev.langchain4j.store.embedding.filter.MetadataFilterBuilder.metadataKey;

public class ChatUtil {

    public static ContentRetriever buildContentRetriever(ChatReq req) {
        List<String> knowledgeIds = req.getKnowledgeIds();
        Function<Query, Filter> filter = (query) -> metadataKey(KNOWLEDGE).isIn(knowledgeIds);
        EmbeddingProvider embeddingProvider = SpringUtil.getBean(EmbeddingProvider.class);
        return EmbeddingStoreContentRetriever.builder()
                .embeddingStore(embeddingProvider.getEmbeddingStore(knowledgeIds))
                .embeddingModel(embeddingProvider.getEmbeddingModel(knowledgeIds))
                .dynamicFilter(filter)
                /*
                * 较小的值（1-3）：适用于只需要最相关内容的场景，如直接回答问题
                * 中等值（5-10）：适用于需要更多上下文或多样化信息的场景
                * 较大的值（10-20+）：适用于需要广泛信息收集或复杂问题解决的场景
                * */
                .maxResults(req.getMaxResults() == 0 ? 3 : req.getMaxResults())
                /*
                * 0.3-0.5：宽松阈值，允许较低相关性的结果，适合广泛探索
                * 0.5-0.7：中等阈值，平衡相关性和结果数量
                * 0.7-0.9：严格阈值，只返回高度相关的结果
                * 0.9：非常严格，可能只返回几乎完全匹配的结果
                * */
                .minScore(req.getMinScore()==0 ? 0.7 : req.getMinScore())
                .build();
    }

    public static void setupKnowledgeRetrieval(AiServices<Agent> aiServices, ContentRetriever contentRetriever) {
        ContentRetriever deduplicatedRetriever = new DeduplicatingContentRetriever(contentRetriever);
        aiServices.retrievalAugmentor(DefaultRetrievalAugmentor
                .builder()
                .contentRetriever(deduplicatedRetriever)
                .build());
    }
}
