package com.sanyth.langchain.core.util;

import dev.langchain4j.rag.content.Content;
import dev.langchain4j.rag.content.retriever.ContentRetriever;
import dev.langchain4j.rag.query.Query;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class DeduplicatingContentRetriever implements ContentRetriever {
    private final ContentRetriever delegate;
    
    public DeduplicatingContentRetriever(ContentRetriever delegate) {
        this.delegate = delegate;
    }
    
    @Override
    public List<Content> retrieve(Query query) {
        // 调用原始检索器获取结果
        List<Content> originalResults = delegate.retrieve(query);
        // 对结果进行去重
        return deduplicateContents(originalResults);
    }
    
    private List<Content> deduplicateContents(List<Content> contents) {
        Map<String, Content> uniqueContents = new LinkedHashMap<>();
        
        for (Content content : contents) {
            // 使用文档名称作为唯一标识符
            String uniqueKey = content.textSegment().metadata().getString("docsName");
            // 只保留每个唯一内容的第一次出现
            if (!uniqueContents.containsKey(uniqueKey)) {
                uniqueContents.put(uniqueKey, content);
            }
        }
        
        return new ArrayList<>(uniqueContents.values());
    }
}
