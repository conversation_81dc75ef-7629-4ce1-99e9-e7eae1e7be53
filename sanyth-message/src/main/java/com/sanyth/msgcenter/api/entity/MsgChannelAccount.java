package com.sanyth.msgcenter.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
/**
 * 渠道账号信息
 *
 * <AUTHOR>
 * @since 2024-08-12 09:35:35
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_MSG_CHANNEL_ACCOUNT")
@Entity
@Table(name = "SYT_MSG_CHANNEL_ACCOUNT")
public class MsgChannelAccount implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 渠道名称
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 业务方标识(xgxt,zsxt)
     */
    @Column(name = "BIZ_ID")
    @TableField("BIZ_ID")
    private String bizId;

    /**
     * 发送渠道
     */
    @Column(name = "SEND_CHANNEL")
    @TableField("SEND_CHANNEL")
    private Integer sendChannel;

    /**
     * 账号配置
     */
    @Column(name = "ACCOUNT_CONFIG", columnDefinition = "VARCHAR2(4000)")
    @TableField("ACCOUNT_CONFIG")
    private String accountConfig;

    /**
     * 状态
     */
    @Column(name = "STATUS")
    @TableField("STATUS")
    private String status;

    /**
     * 创建人
     */
    @Column(name = "CREATOR")
    @TableField("CREATOR")
    private String creator;

    /**
     * 创建时间
     */
    @Column(name = "CREATED")
    @TableField("CREATED")
    private LocalDateTime created;

}
