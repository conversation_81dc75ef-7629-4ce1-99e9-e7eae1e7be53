package com.sanyth.msgcenter.biz.action.consumer;

import com.sanyth.msgcenter.biz.domain.TaskInfo;
import com.sanyth.msgcenter.biz.handler.HandlerHolder;
import com.sanyth.msgcenter.biz.pipeline.BusinessProcess;
import com.sanyth.msgcenter.biz.pipeline.ProcessContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 发送消息，路由到对应的渠道下发消息
 */
@Service
public class SendMessageAction implements BusinessProcess<TaskInfo> {
    @Autowired
    private HandlerHolder handlerHolder;

    @Override
    public void process(ProcessContext<TaskInfo> context) {
        TaskInfo taskInfo = context.getProcessModel();
        handlerHolder.route(taskInfo.getSendChannel()).doHandler(taskInfo);
    }
}
