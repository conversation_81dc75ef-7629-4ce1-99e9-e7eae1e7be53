package com.sanyth.msgcenter.biz.action.send;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Throwables;
import com.sanyth.core.web.Resp;
import com.sanyth.msgcenter.api.entity.MsgMessageTemplate;
import com.sanyth.msgcenter.api.service.MsgMessageTemplateService;
import com.sanyth.msgcenter.biz.domain.MessageParam;
import com.sanyth.msgcenter.biz.domain.SendRequest;
import com.sanyth.msgcenter.biz.domain.SendTaskModel;
import com.sanyth.msgcenter.biz.domain.TaskInfo;
import com.sanyth.msgcenter.biz.dto.model.ContentModel;
import com.sanyth.msgcenter.biz.enums.ChannelType;
import com.sanyth.msgcenter.biz.enums.RespStatusEnum;
import com.sanyth.msgcenter.biz.pipeline.BusinessProcess;
import com.sanyth.msgcenter.biz.pipeline.ProcessContext;
import com.sanyth.msgcenter.biz.utils.ContentHolderUtil;
import com.sanyth.msgcenter.biz.utils.TaskInfoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.*;

/**
 * @description 拼装参数
 */
@Slf4j
@Service
public class SendAssembleAction implements BusinessProcess<SendTaskModel> {

    private static final String LINK_NAME = "url";

    @Override
    public void process(ProcessContext<SendTaskModel> context) {
        SendTaskModel sendTaskModel = context.getProcessModel();
        SendRequest sendRequest = sendTaskModel.getSendRequest();
        String messageTemplateId = sendRequest.getBizId() + sendRequest.getBizModule();

        try {

            List<TaskInfo> taskInfos = assembleTaskInfo(sendTaskModel, sendRequest);
            sendTaskModel.setTaskInfo(taskInfos);
        } catch (Exception e) {
            context.setNeedBreak(true).setResponse(Resp.error(RespStatusEnum.SERVICE_ERROR.getMsg()));
            log.error("assemble task fail! templateId:{}, e:{}", messageTemplateId, Throwables.getStackTraceAsString(e));
        }

    }

    /**
     * 组装 TaskInfo 任务消息
     *
     * @param sendTaskModel
     */
    private List<TaskInfo> assembleTaskInfo(SendTaskModel sendTaskModel, SendRequest sendRequest) {
        List<MessageParam> messageParamList = sendTaskModel.getMessageParamList();
        List<TaskInfo> taskInfoList = new ArrayList<>();

        for (MessageParam messageParam : messageParamList) {

            TaskInfo taskInfo = TaskInfo.builder()
                    .messageId(TaskInfoUtils.generateMessageId())
                    .bizId(sendRequest.getBizId())
                    .messageTemplateId(sendRequest.getBizId() + sendRequest.getBizModule())
                    .businessId(TaskInfoUtils.generateBusinessId(sendRequest.getBizId() + sendRequest.getBizModule()
                            , sendRequest.getCallType()))
                    .receiver(new HashSet<>(Arrays.asList(messageParam.getReceiver().split(String.valueOf(StrPool.C_COMMA)))))
                    .sendChannel(messageParam.getSendChannel())
                    .templateType(sendRequest.getCallType())
                    .msgType(messageParam.getMsgType())
                    .shieldType(sendRequest.getShieldType())
                    .sendAccount(sendRequest.getSendAccount())
                    .contentModel(getContentModelValue(sendRequest, messageParam)).build();

            if (CharSequenceUtil.isBlank(taskInfo.getBizId())) {
                taskInfo.setBizId(taskInfo.getMessageId());
            }

            taskInfoList.add(taskInfo);
        }
        return taskInfoList;
    }

    /**
     * 获取 contentModel，替换模板msgContent中占位符信息
     */
    private static ContentModel getContentModelValue(SendRequest sendRequest, MessageParam messageParam) {

        // 得到真正的ContentModel 类型
        Integer sendChannel = messageParam.getSendChannel();
        Class<? extends ContentModel> contentModelClass = ChannelType.getChanelModelClassByCode(sendChannel);

        // 得到模板的 msgContent 和 入参
//        Map<String, String> variables = messageParam.getVariables();
        JSONObject jsonObject = JSON.parseObject(messageParam.getMsgContent());


        // 通过反射 组装出 contentModel
        Field[] fields = ReflectUtil.getFields(contentModelClass);
        ContentModel contentModel = ReflectUtil.newInstance(contentModelClass);
        for (Field field : fields) {
            String originValue = jsonObject.getString(field.getName());
            //不考虑参数模板化，直接传什么就是什么
            /*if (CharSequenceUtil.isNotBlank(originValue)) {
                String resultValue = ContentHolderUtil.replacePlaceHolder(originValue, variables);
                ReflectUtil.setFieldValue(contentModel, field, resultValue);
            }*/
            ReflectUtil.setFieldValue(contentModel, field, originValue);
        }

        // 如果 url 字段存在，则在url拼接对应的埋点参数
        String url = (String) ReflectUtil.getFieldValue(contentModel, LINK_NAME);
        if (CharSequenceUtil.isNotBlank(url)) {
            String resultUrl = TaskInfoUtils.generateUrl(url, sendRequest.getBizId()+sendRequest.getBizModule()
                    , sendRequest.getCallType());
            ReflectUtil.setFieldValue(contentModel, LINK_NAME, resultUrl);
        }
        return contentModel;
    }
}
