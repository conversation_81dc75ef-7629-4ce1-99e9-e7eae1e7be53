package com.sanyth.msgcenter.biz.handler.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Throwables;
import com.sanyth.msgcenter.api.entity.MsgSmsRecord;
import com.sanyth.msgcenter.api.param.MsgSmsRecordParam;
import com.sanyth.msgcenter.api.service.MsgSmsRecordService;
import com.sanyth.msgcenter.biz.config.MessageTypeSmsConfig;
import com.sanyth.msgcenter.biz.constant.MsgConstant;
import com.sanyth.msgcenter.biz.domain.RecallTaskInfo;
import com.sanyth.msgcenter.biz.domain.SmsParam;
import com.sanyth.msgcenter.biz.domain.TaskInfo;
import com.sanyth.msgcenter.biz.dto.account.sms.SmsAccount;
import com.sanyth.msgcenter.biz.dto.model.SmsContentModel;
import com.sanyth.msgcenter.biz.enums.ChannelType;
import com.sanyth.msgcenter.biz.handler.BaseHandler;
import com.sanyth.msgcenter.biz.script.SmsScript;
import com.sanyth.msgcenter.biz.utils.AccountUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 短信发送处理
 */
@Component
@Slf4j
public class SmsHandler extends BaseHandler {

    @Autowired
    private MsgSmsRecordService msgSmsRecordService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private AccountUtils accountUtils;

    public SmsHandler() {
        channelCode = ChannelType.SMS.getCode();
    }

    @Override
    public boolean handler(TaskInfo taskInfo) {
        SmsParam smsParam = SmsParam.builder()
                .phones(taskInfo.getReceiver())
                .content(getSmsContent(taskInfo))
                .sendChannel(taskInfo.getSendChannel())
                .bizId(taskInfo.getBizId())
                .messageTemplateId(taskInfo.getMessageTemplateId())
                .build();
        try {
            SmsAccount account = accountUtils.getAccount(taskInfo.getSendChannel(),taskInfo.getBizId(), SmsAccount.class);
            smsParam.setScriptName(account.getScriptName());
            smsParam.setSendAccountId(taskInfo.getSendAccount());

            List<MsgSmsRecord> recordList = applicationContext.getBean(account.getScriptName(), SmsScript.class).send(smsParam);
            if (CollUtil.isNotEmpty(recordList)) {
                msgSmsRecordService.saveBatch(recordList);
                return true;
            }
        } catch (Exception e) {
            log.error("SmsHandler#handler fail:{},params:{}", Throwables.getStackTraceAsString(e), JSON.toJSONString(smsParam));
        }
        return false;
    }


    /**
     * 如果有输入链接，则把链接拼在文案后
     * <p>
     * PS: 这里可以考虑将链接 转 短链
     * PS: 如果是营销类的短信，需考虑拼接 回TD退订 之类的文案
     */
    private String getSmsContent(TaskInfo taskInfo) {
        SmsContentModel smsContentModel = (SmsContentModel) taskInfo.getContentModel();
        if (CharSequenceUtil.isNotBlank(smsContentModel.getUrl())) {
            return smsContentModel.getContent() + CharSequenceUtil.SPACE + smsContentModel.getUrl();
        } else {
            return smsContentModel.getContent();
        }
    }

    /**
     * 短信不支持撤回
     * 腾讯云文档 eg：https://cloud.tencent.com/document/product/382/52077
     * @param recallTaskInfo
     */
    @Override
    public void recall(RecallTaskInfo recallTaskInfo) {

    }
}
