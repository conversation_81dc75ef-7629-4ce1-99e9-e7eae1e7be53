package com.sanyth.msgcenter.biz.mq.springeventbus;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 描述：消息
 *
 */
@Getter
public class MessageSpringEventBusEvent extends ApplicationEvent {

    private MessageSpringEventSource messageSpringEventSource;

    public MessageSpringEventBusEvent(Object source, MessageSpringEventSource messageSpringEventSource) {
        super(source);
        this.messageSpringEventSource = messageSpringEventSource;
    }

}
