package com.sanyth.msgcenter.biz.receiver.springeventbus;

import com.alibaba.fastjson.JSON;
import com.sanyth.msgcenter.biz.constant.MessageQueuePipeline;
import com.sanyth.msgcenter.biz.domain.RecallTaskInfo;
import com.sanyth.msgcenter.biz.domain.TaskInfo;
import com.sanyth.msgcenter.biz.mq.springeventbus.MessageSpringEventBusEvent;
import com.sanyth.msgcenter.biz.receiver.MessageReceiver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

@Service
@ConditionalOnProperty(name = "message.mq.pipeline", havingValue = MessageQueuePipeline.SPRING_EVENT_BUS)
public class SpringEventBusReceiverListener implements ApplicationListener<MessageSpringEventBusEvent>, MessageReceiver {

    @Autowired
    private SpringEventBusReceiver springEventBusReceiver;

    @Value("${message.business.topic.name}")
    private String sendTopic;
    @Value("${message.business.recall.topic.name}")
    private String recallTopic;

    @Override
    public void onApplicationEvent(MessageSpringEventBusEvent event) {
        String topic = event.getMessageSpringEventSource().getTopic();
        String jsonValue = event.getMessageSpringEventSource().getJsonValue();
        if (topic.equals(sendTopic)) {
            springEventBusReceiver.consume(JSON.parseArray(jsonValue, TaskInfo.class));
        } else if (topic.equals(recallTopic)) {
            springEventBusReceiver.recall(JSON.parseObject(jsonValue, RecallTaskInfo.class));
        }
    }
}
