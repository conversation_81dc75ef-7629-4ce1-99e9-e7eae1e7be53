package com.sanyth.msgcenter.biz.service.impl;

import com.sanyth.core.annotation.OperationLog;
import com.sanyth.core.web.Resp;
import com.sanyth.msgcenter.biz.domain.*;
import com.sanyth.msgcenter.biz.enums.RespStatusEnum;
import com.sanyth.msgcenter.biz.pipeline.ProcessContext;
import com.sanyth.msgcenter.biz.pipeline.ProcessController;
import com.sanyth.msgcenter.biz.service.SendService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class SendServiceImpl implements SendService {

    @Autowired
    @Qualifier("apiProcessController")
    private ProcessController processController;

    @Override
    public SendResponse send(SendRequest sendRequest) {
        if (ObjectUtils.isEmpty(sendRequest)) {
            return new SendResponse(Integer.valueOf(RespStatusEnum.CLIENT_BAD_PARAMETERS.getCode()), RespStatusEnum.CLIENT_BAD_PARAMETERS.getMsg(), null);
        }

        SendTaskModel sendTaskModel = SendTaskModel.builder()
                .sendRequest(sendRequest)
                .messageParamList(sendRequest.getMessageParamList())
                .build();

        ProcessContext context = ProcessContext.builder()
                .code(sendRequest.getCode())
                .processModel(sendTaskModel)
                .needBreak(false)
                .response(Resp.success()).build();

        ProcessContext process = processController.process(context);

        return new SendResponse(process.getResponse().getCode(), process.getResponse().getMsg(), (List<SimpleTaskInfo>) process.getResponse().getInfo());
    }

}
