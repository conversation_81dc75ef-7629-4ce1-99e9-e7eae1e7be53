# 端口
server:
  port: 9096
  tomcat:
    uri-encoding: utf-8
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true

message:
  mq:
    pipeline: eventBus
  business:
    upload:
      crowd:
        path: /tmp
    log:
      topic:
        name: messageTraceLog
    topic:
      name: messageBusiness
    tagId:
      key: message_tag_id
      value: com.sanyth.message
    recall:
      topic:
        name: messageRecall
# 多环境配置
spring:
  application:
    name: sanythServer
  profiles:
    active: dev
  main:
    allow-bean-definition-overriding: true
  cache:
    type: redis
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 200MB
  mvc:
    async:
      request-timeout: 3600000
  # json时间格式设置
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss


  # 连接池配置
  datasource:
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 30000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      test-while-idle: true
      test-on-borrow: true
      test-on-return: false
      remove-abandoned: true
      remove-abandoned-timeout: 1800
      #pool-prepared-statements: false
      #max-pool-prepared-statement-per-connection-size: 20
      filters: stat, wall
      validation-query: SELECT 1 FROM DUAL
      aop-patterns: com.sanyth.*.*.service.*
      stat-view-servlet:
        url-pattern: /druid/*
        reset-enable: true
        login-username: admin
        login-password: sanyth123456
  # jpa配置
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.OracleDialect
        show_sql: true
        format_sql: true
        hbm2ddl:
#          auto: update
          auto: none

  # 邮件服务器配置

#  mail:
#    host: smtp.qq.com
#    username:
#    password:
#    default-encoding: UTF-8
#    properties:
#      mail:
#        smtp:
#          auth: true
#          socketFactory:
#            class: javax.net.ssl.SSLSocketFactory
#            port: 465

# Mybatis-plus配置
mybatis-plus:
  mapper-locations: classpath*:com/sanyth/**/*Mapper.xml
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
  global-config:
    :banner: false
    db-config:
      id-type: auto
      logic-delete-value: 1
      logic-not-delete-value: 0

# 框架配置
config:
  open-office-home: C:/OpenOffice4/
  token-key: ULgNsWJ8rPjRtnjzX/Gv2RGS80Ksnm/ZaLpvIL+NrBg=
  temp-file-dir: C:/Develop/WorkSpace_syt/ele-admin-api/temp/

