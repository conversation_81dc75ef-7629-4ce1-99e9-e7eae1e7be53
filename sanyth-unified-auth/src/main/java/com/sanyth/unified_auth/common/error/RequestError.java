package com.sanyth.unified_auth.common.error;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025/6/10 9:45
 */
@EqualsAndHashCode(callSuper = false)
@Data
public class RequestError extends RuntimeException {

    private String content;

    public static RequestError of(String content) {
        RequestError error = new RequestError();
        error.setContent(content);
        return error;
    }

    public static void isTrue(boolean expression, String content) throws RequestError {
        if (!expression) {
            throw RequestError.of(content);
        }
    }
}
