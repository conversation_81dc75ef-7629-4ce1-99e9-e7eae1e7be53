package com.sanyth.unified_auth.common.repository;

import com.sanyth.unified_auth.common.entity.BusinessSystem;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

public interface BusinessSystemRepository extends PagingAndSortingRepository<BusinessSystem, Long>, CrudRepository<BusinessSystem, Long>, JpaSpecificationExecutor<BusinessSystem> {
    boolean existsByToken(String token);

    BusinessSystem findByToken(String token);
}
