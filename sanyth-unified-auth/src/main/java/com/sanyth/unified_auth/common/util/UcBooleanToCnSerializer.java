package com.sanyth.unified_auth.common.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.sanyth.core.enums.JudgeMark;

import java.io.IOException;

/**
 * @since 2025/7/15 14:01
 */
public class UcBooleanToCnSerializer extends JsonSerializer<Boolean> {

    @Override
    public void serialize(Boolean value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeString(Boolean.TRUE.equals(value) ? JudgeMark.yes.getText() : JudgeMark.no.getText());
    }
}
