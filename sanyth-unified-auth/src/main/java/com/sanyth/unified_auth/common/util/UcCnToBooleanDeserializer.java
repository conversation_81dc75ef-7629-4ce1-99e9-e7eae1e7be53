package com.sanyth.unified_auth.common.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.sanyth.core.enums.JudgeMark;

import java.io.IOException;

/**
 * @since 2025/7/15 14:01
 */
public class UcCnToBooleanDeserializer extends JsonDeserializer<Boolean> {

    @Override
    public Boolean deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String text = p.getText();
        return JudgeMark.yes.getText().equals(text);
    }
}
