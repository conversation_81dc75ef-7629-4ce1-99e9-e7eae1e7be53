package com.sanyth.unified_auth.common.util;

import com.sanyth.core.exception.BusinessException;

/**
 * @since 2025/6/22 10:54
 */
public class UnifiedAuthUtil {

    public static void throwException(String message) throws BusinessException {
        throw new BusinessException(message);
    }

    public static void checkTrue(boolean expression, String content) throws BusinessException {
        if (!expression) {
            throw new BusinessException(content);
        }
    }

}
