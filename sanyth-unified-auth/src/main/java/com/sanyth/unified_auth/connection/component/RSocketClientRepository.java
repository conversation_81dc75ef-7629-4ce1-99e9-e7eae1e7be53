package com.sanyth.unified_auth.connection.component;

import com.sanyth.unified_auth.common.entity.BusinessSystem;
import com.sanyth.unified_auth.common.error.RequestError;
import com.sanyth.unified_auth.common.repository.BusinessSystemRepository;
import com.sanyth.unified_auth.connection.dto.RSocketClientElement;
import com.sanyth.unified_auth.connection.dto.RSocketPayloadDto;
import io.rsocket.RSocket;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.rsocket.RSocketRequester;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @since 2025/6/9 11:00
 */
@Slf4j
@AllArgsConstructor
@Component
public class RSocketClientRepository {

    private final BusinessSystemRepository businessSystemRepository;
    // token -> requester
    private final Map<String, RSocketClientElement> tokenToRequester = new ConcurrentHashMap<>();
    // requester -> token
    private final Map<RSocketRequester, String> requesterToToken = new ConcurrentHashMap<>();

    public void register(RSocketPayloadDto payload, RSocketRequester requester) {
        RSocket rsocket = requester.rsocket();
        if (rsocket == null) {
            throw new RuntimeException("requester.rsocket() is null");
        }

        String token = payload.getToken();

        BusinessSystem businessSystem = businessSystemRepository.findByToken(token);
        if (businessSystem == null) {
            log.error("Invalid token, closing connection");

            // 主动关闭连接（没有标准方式强制断，但可以不保存 requester）
            // 或抛出异常
            throw new IllegalArgumentException("Invalid token");
        }
        log.info("Client authenticated with token: {}", payload);


//        clients.put(token, requester);
//        webAuthIndexMap.put(token, payload.getWebAuthIndexUrl());
        tokenToRequester.put(token, new RSocketClientElement(requester, payload.getWebAuthIndexUrl(), payload.getMobileIndexUrl(), businessSystem.getId()));
        requesterToToken.put(requester, token);

        rsocket.onClose()
                .doFirst(() -> {
//                    System.out.println("Client " + token + " connected");
                    log.info("Client {} connected", token);
                })
                .doOnError(error -> {
//                    System.out.println("Client error: " + error.getMessage());
                    log.error("Client error: {}", error.getMessage());
                })
                .doFinally(signal -> {
//                    System.out.println("Client " + token + " disconnected: " + signal);
                    log.info("Client {} disconnected: {}", token, signal);
                    tokenToRequester.remove(token);
                    requesterToToken.remove(requester);
                })
                .subscribe();
    }

//    public RSocketRequester getClient(String token) {
//        return tokenToRequester.get(token).getRequester();
//    }

    public RSocketClientElement getClientDto(String token) {
        RSocketClientElement clientElement = tokenToRequester.get(token);

        RSocketRequester requester = clientElement.getRequester();
        RequestError.isTrue(requester != null, "尚未连接");
        RequestError.isTrue(!requester.isDisposed(), "连接已断开");

        String webAuthIndexUrl = clientElement.getWebAuthIndexUrl();
        RequestError.isTrue(webAuthIndexUrl != null, "缺失业务系统重定向链接");
        return clientElement;
    }

    public String getToken(RSocketRequester requester) {
        return requesterToToken.get(requester);
    }

}
