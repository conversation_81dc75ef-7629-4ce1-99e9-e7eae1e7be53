package com.sanyth.unified_auth.connection.util;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * @since 2025/6/26 11:36
 */
public class ConnUtil {

    public static String encodeParam(String param1, String param2) {
        // 使用某种分隔符，建议选择 URL 安全且不会出现在参数中的字符（如 \t）
        String combined = param1 + "\t" + param2;
        String base64 = Base64.getEncoder().encodeToString(combined.getBytes(StandardCharsets.UTF_8));
        return URLEncoder.encode(base64, StandardCharsets.UTF_8);
    }

    public static String[] decodeParams(String encodedData) {
        String base64 = URLDecoder.decode(encodedData, StandardCharsets.UTF_8);
        String combined = new String(Base64.getDecoder().decode(base64), StandardCharsets.UTF_8);
        return combined.split("\t", 2); // 最多分成两个部分
    }
}
