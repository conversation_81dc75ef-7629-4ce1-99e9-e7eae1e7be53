package com.sanyth.unified_auth.platform.controller;

import com.sanyth.unified_auth.common.form.IdTextPairForm;
import com.sanyth.unified_auth.common.query.QuerySuper;
import com.sanyth.unified_auth.common.util.UnifiedAuthUtil;
import com.sanyth.unified_auth.common.vo.PageVo;
import com.sanyth.unified_auth.platform.entity.PlatformAppSuper;
import com.sanyth.unified_auth.platform.enums.PlatformType;
import com.sanyth.unified_auth.platform.form.PlatformAppForm;
import com.sanyth.unified_auth.platform.repository.PlatformAppRepository;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * @since 2025/7/12 15:14
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/unified-auth/admin/platform/app")
public class PlatformAppController {

    private final PlatformAppRepository appRepository;

    /**
     * 分页查询
     */
    @GetMapping("page")
    public PageVo<PlatformAppForm> page(QuerySuper query, Long bizId, String nameLike) {
        UnifiedAuthUtil.checkTrue(bizId != null, "bizId is required");

        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(),
                Sort.by(Sort.Order.asc("sequenceNum"), Sort.Order.desc("createdAt")));

        Page<PlatformAppSuper> page = appRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> criteriaBuilder.equal(root.get("businessSystem").get("id"), bizId),
                (root, query1, criteriaBuilder) -> {
                    if (nameLike != null)
                        return criteriaBuilder.like(root.get("name"), "%" + nameLike + "%");
                    return null;
                }
        ), pageRequest);

        return new PageVo<>(page.getContent().stream().map(PlatformAppForm::new).toList(), page.getTotalElements());
    }

    @GetMapping("optionalType")
    public List<IdTextPairForm> optionalType() {
        return Arrays.stream(PlatformType.values()).map(t -> new IdTextPairForm(t.name(), t.getText())).toList();
    }

//    @GetMapping("fieldsByType")
//    public List<IdTextPairForm> fieldsByType(PlatformType type) {
////        return Arrays.stream(PlatformType.values()).map(t -> new IdTextPairForm(t.name(), t.getText())).toList();
//        List<IdTextPairForm> list = new ArrayList<>();
//        if (type == PlatformType.DINGTALK) {
//            list.add(new IdTextPairForm("corpId", "CorpId"));
//            list.add(new IdTextPairForm("unifiedAppId", "App ID"));
//            list.add(new IdTextPairForm("agentId", "AgentId"));
//            list.add(new IdTextPairForm("clientId", "Client ID"));
//            list.add(new IdTextPairForm("clientSecret", "Client Secret"));
//        }
//        return list;
//    }

//    @PostMapping("operation")
//    public void operation(@RequestBody PlatformAppForm form) {
//        PlatformType type  = PlatformType.valueOf(form.getType().getId());
//         switch (type) {
//            case DINGTALK -> new DingtalkApp();
//            case WECHAT -> new WechatApp();
//            case WXWORK -> new WxworkApp();
//            case YIBAN -> new YibanApp();
//        };
//
//        PlatformAppSuper entity;
//
//        if (form.getId() != null) {
//            entity = appRepository.findById(form.getId()).orElseThrow();
//            entity.setUpdatedAt(LocalDateTime.now());
//            type = PlatformUtil.getPlatform(entity);
//        } else {
//            type = PlatformType.valueOf(form.getType().getId());
//            entity = switch (type) {
//                case DINGTALK -> new DingtalkApp();
//                case WECHAT -> new WechatApp();
//                case WXWORK -> new WxworkApp();
//                case YIBAN -> new YibanApp();
//            };
//            entity.setCreatedAt(LocalDateTime.now());
//
////            RequestError.isTrue(form.getBizId() != null, "bizId is required");
//            UnifiedAuthUtil.checkTrue(form.getBizId() != null, "bizId is required");
//            BusinessSystem businessSystem = new BusinessSystem();
//            businessSystem.setId(form.getBizId());
//            entity.setBusinessSystem(businessSystem);
//        }
//
//        entity.setName(form.getName());
//        entity.setEnabled(form.getEnabled());
//        entity.setSequenceNum(form.getSequenceNum());
//
//
//        appRepository.save(entity);
//    }

    @PostMapping("remove")
    public void remove(@RequestBody Long[] ids) {
        appRepository.deleteAllById(List.of(ids));
    }

}
