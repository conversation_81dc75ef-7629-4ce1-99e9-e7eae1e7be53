package com.sanyth.unified_auth.platform.dingtalk.controller;

import com.sanyth.unified_auth.common.entity.BusinessSystem;
import com.sanyth.unified_auth.common.query.QuerySuper;
import com.sanyth.unified_auth.common.util.UnifiedAuthUtil;
import com.sanyth.unified_auth.common.vo.PageVo;
import com.sanyth.unified_auth.platform.dingtalk.entity.DingtalkApp;
import com.sanyth.unified_auth.platform.dingtalk.form.DingtalkAppForm;
import com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkAppRepository;
import com.sanyth.unified_auth.platform.util.PlatformUtil;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 平台/钉钉/应用管理
 *
 * @since 2025/6/17 17:57
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/unified-auth/admin/dingtalk/app")
public class DingtalkAppController {

    private final DingtalkAppRepository appRepository;

    /**
     * 分页查询
     */
    @GetMapping("page")
    public PageVo<DingtalkAppForm> page(QuerySuper query, Long bizId, String nameLike) {
        UnifiedAuthUtil.checkTrue(bizId != null, "bizId is required");

        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("createdAt").ascending());

        Page<DingtalkApp> page = appRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> criteriaBuilder.equal(root.get("businessSystem").get("id"), bizId),
                (root, query1, criteriaBuilder) -> {
                    if (StringUtils.hasText(nameLike))
                        return criteriaBuilder.like(root.get("name"), "%" + nameLike + "%");
                    return null;
                }
        ), pageRequest);

        return new PageVo<>(page.getContent().stream().map(DingtalkAppForm::new).toList(), page.getTotalElements());
    }

    @GetMapping("/{id}")
    public DingtalkAppForm get(@PathVariable("id") Long id) {
        Optional<DingtalkApp> item = appRepository.findById(id);
        return new DingtalkAppForm(item.orElseThrow());
    }

    /**
     * 添加或修改
     */
    @PostMapping("operation")
    public void operation(@RequestBody DingtalkAppForm form) {
        DingtalkApp entity;
        if (form.getId() != null) {
            entity = appRepository.findById(form.getId()).orElseThrow();
            entity.setUpdatedAt(LocalDateTime.now());
        } else {
            entity = new DingtalkApp();
            entity.setCreatedAt(LocalDateTime.now());

//            RequestError.isTrue(form.getBizId() != null, "bizId is required");
            UnifiedAuthUtil.checkTrue(form.getBizId() != null, "bizId is required");
            BusinessSystem businessSystem = new BusinessSystem();
            businessSystem.setId(form.getBizId());
            entity.setBusinessSystem(businessSystem);
        }

        PlatformUtil.appOperationCommonCheck(entity, form);

        entity.setCorpId(form.getCorpId());
        entity.setUnifiedAppId(form.getUnifiedAppId());
        entity.setAgentId(form.getAgentId());
        entity.setClientId(form.getClientId());
        entity.setClientSecret(form.getClientSecret());

        appRepository.save(entity);
    }

    /**
     * 批量删除
     */
    @PostMapping("delete")
    public void delete(@RequestParam Long[] ids) {
        appRepository.deleteAllById(List.of(ids));
    }
}
