package com.sanyth.unified_auth.platform.dingtalk.controller;

import com.sanyth.unified_auth.common.query.QuerySuper;
import com.sanyth.unified_auth.common.util.UnifiedAuthUtil;
import com.sanyth.unified_auth.common.vo.PageVo;
import com.sanyth.unified_auth.platform.dingtalk.entity.DingtalkApp;
import com.sanyth.unified_auth.platform.dingtalk.entity.DingtalkUser;
import com.sanyth.unified_auth.platform.dingtalk.form.DingtalkUserForm;
import com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkUserRepository;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 平台/钉钉/用户管理
 *
 * @since 2025/4/6 19:09
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/unified-auth/admin/dingtalk/user")
public class DingtalkUserController {

    private final DingtalkUserRepository userRepository;

    /**
     * 分页查询
     * 权限标识：platform:dingtalk:user:list
     */
    @GetMapping("page")
    public PageVo<DingtalkUserForm> page(QuerySuper query, DingtalkUserForm form) {
        Long appId = form.getAppId();
        UnifiedAuthUtil.checkTrue(appId != null, "appId is required");
        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("createdAt").ascending());

        Page<DingtalkUser> page = userRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> criteriaBuilder.equal(root.get("app").get("id"), appId),
                (root, query1, criteriaBuilder) -> {
                    if (StringUtils.hasText(form.getBizUserId()))
                        return criteriaBuilder.like(root.get("bizUserId"), "%" + form.getBizUserId() + "%");
                    return null;
                },
                (root, query1, criteriaBuilder) -> {
                    if (StringUtils.hasText(form.getUserId()))
                        return criteriaBuilder.like(root.get("userId"), "%" + form.getUserId() + "%");
                    return null;
                },
                (root, query1, criteriaBuilder) -> {
                    if (StringUtils.hasText(form.getName()))
                        return criteriaBuilder.like(root.get("name"), "%" + form.getName() + "%");
                    return null;
                }
        ), pageRequest);

        return new PageVo<>(page.getContent().stream().map(DingtalkUserForm::new).toList(), page.getTotalElements());
    }

    @GetMapping("/{id}")
    public DingtalkUserForm get(@PathVariable("id") Long id) {
        Optional<DingtalkUser> item = userRepository.findById(id);
        return new DingtalkUserForm(item.orElseThrow());
    }

    @PostMapping("operation")
    public void operation(@RequestBody DingtalkUserForm form) {
        DingtalkUser entity;
        if (form.getId() != null) {
            entity = userRepository.findById(form.getId()).orElseThrow();
            entity.setUpdatedAt(LocalDateTime.now());
        } else {
            entity = new DingtalkUser();
            entity.setCreatedAt(LocalDateTime.now());

            UnifiedAuthUtil.checkTrue(form.getAppId() != null, "appId is required");
            DingtalkApp app = new DingtalkApp();
            app.setId(form.getAppId());
            entity.setApp(app);
        }

        UnifiedAuthUtil.checkTrue(StringUtils.hasText(form.getBizUserId()), "业务用户ID不能为空");
        entity.setBizUserId(form.getBizUserId());
        entity.setUserId(form.getUserId());
        entity.setName(form.getName());

        userRepository.save(entity);
    }

    @PostMapping("remove")
    public void remove(@RequestBody Long[] ids) {
        userRepository.deleteAllById(List.of(ids));
    }
}
