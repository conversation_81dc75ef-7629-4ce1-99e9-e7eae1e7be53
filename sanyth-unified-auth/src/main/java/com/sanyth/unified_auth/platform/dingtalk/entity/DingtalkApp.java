package com.sanyth.unified_auth.platform.dingtalk.entity;

import com.sanyth.unified_auth.platform.entity.PlatformAppSuper;
import jakarta.persistence.CascadeType;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.OneToMany;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 钉钉应用表
 *
 * @since 2025/6/17 10:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@DiscriminatorValue("DINGTALK")
public class DingtalkApp extends PlatformAppSuper {

    private String corpId;
    private String unifiedAppId;
    private String agentId;
    private String clientId;
    private String clientSecret;
    @OneToMany(mappedBy = "app", cascade = CascadeType.REMOVE)
    private List<DingtalkUser> users;

}
