package com.sanyth.unified_auth.platform.dingtalk.form;

import com.sanyth.unified_auth.platform.dingtalk.entity.DingtalkApp;
import com.sanyth.unified_auth.platform.form.PlatformAppForm;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @since 2025/4/6 18:49
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class DingtalkAppForm extends PlatformAppForm {

    public DingtalkAppForm(DingtalkApp app) {
        super(app);
//        this.name = app.getName();
        this.corpId = app.getCorpId();
        this.unifiedAppId = app.getUnifiedAppId();
        this.agentId = app.getAgentId();
        this.clientId = app.getClientId();
        this.clientSecret = app.getClientSecret();
    }

//    private Long bizId;
//    private String name;
    private String corpId;
    private String unifiedAppId;
    private String agentId;
    private String clientId;
    private String clientSecret;
}
