package com.sanyth.unified_auth.platform.dingtalk.service.impl;

import com.sanyth.unified_auth.common.error.RequestError;
import com.sanyth.unified_auth.common.util.UnifiedAuthConfig;
import com.sanyth.unified_auth.platform.dingtalk.component.DingtalkApiClient;
import com.sanyth.unified_auth.platform.dingtalk.dto.DingtalkMessageCard;
import com.sanyth.unified_auth.platform.dingtalk.dto.DingtalkMessageText;
import com.sanyth.unified_auth.platform.dingtalk.entity.DingtalkApp;
import com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkAppRepository;
import com.sanyth.unified_auth.platform.dingtalk.repository.DingtalkUserRepository;
import com.sanyth.unified_auth.platform.dingtalk.util.DingtalkConfigUtil;
import com.sanyth.unified_auth.platform.dingtalk.vo.DtJsConfigVo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.CharacterPredicates;
import org.apache.commons.text.RandomStringGenerator;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * @since 2025/6/17 17:21
 */
@AllArgsConstructor
@Service
public class DingtalkOpenServiceImpl implements com.sanyth.unified_auth.platform.dingtalk.service.DingtalkOpenService {

    private final DingtalkAppRepository appRepository;
    private final DingtalkApiClient apiClient;
    private final DingtalkUserRepository userRepository;
    private final UnifiedAuthConfig unifiedAuthConfig;

    @Override
    public DtJsConfigVo getJsapiConfig(String url, Long appId) {
        RequestError.isTrue(StringUtils.isNotEmpty(url), "url is required");
        if (url.contains("#")) {
            url = url.substring(0, url.indexOf("#"));
        }

        DingtalkApp app = appRepository.findById(appId).orElseThrow();
        String jsapiTicket = apiClient.getJsapiTicket(app);
        String agentId = app.getAgentId();
        String corpId = app.getCorpId();

        String nonceStr = RandomStringGenerator.builder().withinRange('0', 'z').filteredBy(CharacterPredicates.DIGITS, CharacterPredicates.LETTERS).get().generate(10);
        long timeStamp = System.currentTimeMillis();
        String signature = DingtalkConfigUtil.sign(jsapiTicket, nonceStr, timeStamp, url);

        return new DtJsConfigVo(agentId, corpId, timeStamp, nonceStr, signature);
    }

    @Override
    public void sendMessageText(Long appId, Collection<String> usernames, String content) {
        DingtalkApp app = appRepository.findById(appId).orElseThrow();

        Set<String> userIds = new HashSet<>(usernames);
        userRepository.findAllByAppAndBizUserIdIn(app, usernames).forEach(user -> userIds.add(user.getUserId()));
        apiClient.sendMessage(app, userIds, new DingtalkMessageText(content), null);
    }

    @Override
    public void sendMessageCard(Long appId, Collection<String> usernames, String title, String markdown, String innerParam) {
        String authOpenUrlPrefix = unifiedAuthConfig.getAuthOpenUrlPrefix();
        RequestError.isTrue(StringUtils.isNotEmpty(authOpenUrlPrefix), "authOpenUrlPrefix is required");

        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(authOpenUrlPrefix);
        builder.path("/unified-auth/open/dingtalk/auth");
        builder.queryParam("id", appId);
        builder.queryParam("inner_param", innerParam);
        String redirectUrl = builder.toUriString();

        DingtalkApp app = appRepository.findById(appId).orElseThrow();

        Set<String> userIds = new HashSet<>(usernames);
        userRepository.findAllByAppAndBizUserIdIn(app, usernames).forEach(user -> userIds.add(user.getUserId()));
        apiClient.sendMessage(app, userIds, null, new DingtalkMessageCard(title, markdown, redirectUrl));
    }

}
