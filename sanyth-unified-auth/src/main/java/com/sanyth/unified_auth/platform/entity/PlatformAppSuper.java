package com.sanyth.unified_auth.platform.entity;

import com.sanyth.unified_auth.common.entity.BusinessSystem;
import com.sanyth.unified_auth.common.entity.EntitySuper;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2025/6/5 13:52
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "unified_auth_platform_app")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE) // 或者 TABLE_PER_CLASS, JOINED
@DiscriminatorColumn(name = "type", discriminatorType = DiscriminatorType.STRING)
public abstract class PlatformAppSuper extends EntitySuper {

    @ManyToOne
//    @JoinColumn(name = "business_system_id", nullable = false)
    private BusinessSystem businessSystem;
    @Column(nullable = false)
    private String name;
    @Column(nullable = false)
    private boolean enabled = true;
    @Column(nullable = false)
    private Integer sequenceNum = 0;

}
