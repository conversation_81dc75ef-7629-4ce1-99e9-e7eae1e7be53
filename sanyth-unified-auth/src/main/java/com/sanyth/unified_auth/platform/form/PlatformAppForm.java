package com.sanyth.unified_auth.platform.form;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sanyth.unified_auth.common.form.FormSuper;
import com.sanyth.unified_auth.common.form.IdTextPairForm;
import com.sanyth.unified_auth.common.util.UcBooleanToCnSerializer;
import com.sanyth.unified_auth.common.util.UcCnToBooleanDeserializer;
import com.sanyth.unified_auth.platform.entity.PlatformAppSuper;
import com.sanyth.unified_auth.platform.enums.PlatformType;
import com.sanyth.unified_auth.platform.util.PlatformUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @since 2025/7/12 15:15
 */
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class PlatformAppForm extends FormSuper {

    public PlatformAppForm(PlatformAppSuper app) {
        super(app);
        this.name = app.getName();
        PlatformType type = PlatformUtil.getPlatform(app);
        if (type != null) {
            this.type = new IdTextPairForm(type.name(), type.getText());
        }
        this.enabled = app.isEnabled();
        this.sequenceNum = app.getSequenceNum();
    }

    private Long bizId;
    private String name;
    private IdTextPairForm type;
    @JsonSerialize(using = UcBooleanToCnSerializer.class)
    @JsonDeserialize(using = UcCnToBooleanDeserializer.class)
    private Boolean enabled;
    private Integer sequenceNum;

}
