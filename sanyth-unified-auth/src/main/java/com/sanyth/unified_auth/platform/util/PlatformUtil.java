package com.sanyth.unified_auth.platform.util;

import com.sanyth.unified_auth.common.error.RequestError;
import com.sanyth.unified_auth.common.util.UnifiedAuthUtil;
import com.sanyth.unified_auth.platform.dingtalk.entity.DingtalkApp;
import com.sanyth.unified_auth.platform.entity.PlatformAppSuper;
import com.sanyth.unified_auth.platform.enums.PlatformType;
import com.sanyth.unified_auth.platform.form.PlatformAppForm;
import com.sanyth.unified_auth.platform.wechat.entity.WechatApp;
import com.sanyth.unified_auth.platform.wxwork.entity.WxworkApp;
import com.sanyth.unified_auth.platform.yiban.entity.YibanApp;
import jakarta.servlet.http.HttpServletRequest;

import java.nio.charset.StandardCharsets;

/**
 * @since 2025/6/11 17:54
 */
public class PlatformUtil {

    public static String getUrlRoot(HttpServletRequest request) {
        String scheme = request.getScheme();
        int serverPort = request.getServerPort();
        String url_root = scheme + "://" + request.getServerName();
        if (serverPort != 80 && serverPort != 443) {
            url_root += ":" + serverPort;
        }

        return url_root;
    }

    public static String urlEncode(String url) {
        if (url == null) {
            return null;
        }
        try {
            return java.net.URLEncoder.encode(url, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw RequestError.of("url encode error");
        }
    }

    public static PlatformType getPlatform(PlatformAppSuper app) {
        if (app instanceof WxworkApp) return PlatformType.WXWORK;
        if (app instanceof DingtalkApp) return PlatformType.DINGTALK;
        if (app instanceof WechatApp) return PlatformType.WECHAT;
        if (app instanceof YibanApp) return PlatformType.YIBAN;
        return null;
    }

    public static void appOperationCommonCheck(PlatformAppSuper entity, PlatformAppForm form) {
        UnifiedAuthUtil.checkTrue(form.getName() != null, "名称不能为空");
        UnifiedAuthUtil.checkTrue(form.getSequenceNum() != null, "排序号不能为空");
        entity.setName(form.getName());
        entity.setEnabled(form.getEnabled());
        entity.setSequenceNum(form.getSequenceNum());
    }
}
