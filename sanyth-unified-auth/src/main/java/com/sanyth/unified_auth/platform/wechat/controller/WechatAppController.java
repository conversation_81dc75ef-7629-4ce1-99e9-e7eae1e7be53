package com.sanyth.unified_auth.platform.wechat.controller;

import com.sanyth.unified_auth.common.entity.BusinessSystem;
import com.sanyth.unified_auth.common.error.RequestError;
import com.sanyth.unified_auth.common.query.QuerySuper;
import com.sanyth.unified_auth.common.util.UnifiedAuthUtil;
import com.sanyth.unified_auth.common.vo.PageVo;
import com.sanyth.unified_auth.platform.util.PlatformUtil;
import com.sanyth.unified_auth.platform.wechat.entity.WechatApp;
import com.sanyth.unified_auth.platform.wechat.form.WechatAppForm;
import com.sanyth.unified_auth.platform.wechat.repository.WechatAppRepository;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * @since 2025/7/1 11:53
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/unified-auth/admin/wechat/app")
public class WechatAppController {

    private final WechatAppRepository appRepository;

    @GetMapping("page")
    public PageVo<WechatAppForm> page(QuerySuper query, Long bizId) {
        UnifiedAuthUtil.checkTrue(bizId != null, "bizId is required");

        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("createdAt").descending());

        Page<WechatApp> page = appRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder)
                        -> criteriaBuilder.equal(root.get("businessSystem").get("id"), bizId)
        ), pageRequest);

        return new PageVo<>(page.getContent().stream().map(WechatAppForm::new).toList(), page.getTotalElements());
    }

    @GetMapping("/{id}")
    public WechatAppForm get(@PathVariable("id") Long id) {
        Optional<WechatApp> item = appRepository.findById(id);
        return new WechatAppForm(item.orElseThrow());
    }

    @PostMapping("operation")
    public void operation(@RequestBody WechatAppForm form) {
        WechatApp entity;
        if (form.getId() != null) {
            entity = appRepository.findById(form.getId()).orElseThrow();
            entity.setUpdatedAt(LocalDateTime.now());
        } else {
            entity = new WechatApp();
            entity.setCreatedAt(LocalDateTime.now());

            RequestError.isTrue(form.getBizId() != null, "bizId is required");
            BusinessSystem businessSystem = new BusinessSystem();
            businessSystem.setId(form.getBizId());
            entity.setBusinessSystem(businessSystem);
        }

        PlatformUtil.appOperationCommonCheck(entity, form);

        entity.setAppId(form.getAppId());
        entity.setAppSecret(form.getAppSecret());
        appRepository.save(entity);
    }

    @PostMapping("delete")
    public void delete(@RequestParam Long[] ids) {
        appRepository.deleteAllById(List.of(ids));
    }
}
