package com.sanyth.unified_auth.platform.wechat.controller;

import com.sanyth.unified_auth.common.query.QuerySuper;
import com.sanyth.unified_auth.common.util.UnifiedAuthUtil;
import com.sanyth.unified_auth.common.vo.PageVo;
import com.sanyth.unified_auth.platform.wechat.entity.WechatApp;
import com.sanyth.unified_auth.platform.wechat.entity.WechatUser;
import com.sanyth.unified_auth.platform.wechat.form.WechatUserForm;
import com.sanyth.unified_auth.platform.wechat.repository.WechatUserRepository;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * @since 2025/7/1 11:57
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/unified-auth/admin/wechat/user")
public class WechatUserController {

    private final WechatUserRepository userRepository;

    @GetMapping("page")
    public PageVo<WechatUserForm> page(QuerySuper query, WechatUserForm form) {
        Long appId = form.getAppId();
        UnifiedAuthUtil.checkTrue(appId != null, "appId is required");

        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("createdAt").descending());

        Page<WechatUser> page = userRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> criteriaBuilder.equal(root.get("app").get("id"), appId),
                (root, query1, criteriaBuilder) -> {
                    if (StringUtils.hasText(form.getBizUserId()))
                        return criteriaBuilder.like(root.get("bizUserId"), "%" + form.getBizUserId() + "%");
                    return null;
                },
                (root, query1, criteriaBuilder) -> {
                    if (StringUtils.hasText(form.getOpenId()))
                        return criteriaBuilder.like(root.get("openId"), "%" + form.getOpenId() + "%");
                    return null;
                }
        ), pageRequest);

        return new PageVo<>(page.getContent().stream().map(WechatUserForm::new).toList(), page.getTotalElements());
    }

    @GetMapping("/{id}")
    public WechatUserForm get(@PathVariable("id") Long id) {
        Optional<WechatUser> item = userRepository.findById(id);
        return new WechatUserForm(item.orElseThrow());
    }

    @PostMapping("operation")
    public void operation(@RequestBody WechatUserForm form) {
        WechatUser entity;
        if (form.getId() != null) {
            entity = userRepository.findById(form.getId()).orElseThrow();
            entity.setUpdatedAt(LocalDateTime.now());
        } else {
            entity = new WechatUser();
            entity.setCreatedAt(LocalDateTime.now());

//            RequestError.isTrue(form.getAppId() != null, "appId is required");
            UnifiedAuthUtil.checkTrue(form.getAppId() != null, "appId is required");
            WechatApp app = new WechatApp();
            app.setId(form.getAppId());
            entity.setApp(app);
        }

        UnifiedAuthUtil.checkTrue(StringUtils.hasText(form.getBizUserId()), "业务用户ID不能为空");
        entity.setOpenId(form.getOpenId());
        entity.setBizUserId(form.getBizUserId());
        userRepository.save(entity);
    }

    @PostMapping("remove")
    public void remove(@RequestBody Long[] ids) {
        userRepository.deleteAllById(List.of(ids));
    }
}
