package com.sanyth.unified_auth.platform.wechat.entity;

import com.sanyth.unified_auth.platform.entity.PlatformAppSuper;
import jakarta.persistence.CascadeType;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.OneToMany;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 微信公众平台应用
 *
 * @since 2025/6/30 16:27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@DiscriminatorValue("WECHAT")
public class WechatApp extends PlatformAppSuper {

    private String appId;
    private String appSecret;
    @OneToMany(mappedBy = "app", cascade = CascadeType.REMOVE)
    private List<WechatUser> users;

}
