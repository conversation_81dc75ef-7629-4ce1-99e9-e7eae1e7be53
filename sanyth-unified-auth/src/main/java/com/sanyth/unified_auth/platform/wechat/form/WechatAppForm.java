package com.sanyth.unified_auth.platform.wechat.form;

import com.sanyth.unified_auth.platform.form.PlatformAppForm;
import com.sanyth.unified_auth.platform.wechat.entity.WechatApp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @since 2025/7/1 11:49
 */
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class WechatAppForm extends PlatformAppForm {

    public WechatAppForm(WechatApp entity) {
        super(entity);
        this.appId = entity.getAppId();
        this.appSecret = entity.getAppSecret();
    }

    private String appId;
    private String appSecret;

}
