package com.sanyth.unified_auth.platform.wxwork.controller;

import com.sanyth.unified_auth.common.entity.BusinessSystem;
import com.sanyth.unified_auth.common.util.UnifiedAuthUtil;
import com.sanyth.unified_auth.common.vo.PageVo;
import com.sanyth.unified_auth.platform.util.PlatformUtil;
import com.sanyth.unified_auth.platform.wxwork.entity.WxworkApp;
import com.sanyth.unified_auth.platform.wxwork.form.WxworkAppForm;
import com.sanyth.unified_auth.platform.wxwork.query.WxworkAppQuery;
import com.sanyth.unified_auth.platform.wxwork.repository.WxworkAppRepository;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * @since 2025/6/10 9:16
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/unified-auth/admin/wxwork/app")
public class WxworkAppController {

    private final WxworkAppRepository wxworkAppRepository;

    @GetMapping("page")
    public PageVo<WxworkAppForm> page(WxworkAppQuery query) {
        UnifiedAuthUtil.checkTrue(query.getBizId() != null, "bizId is required");

        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("createdAt").descending());

        Page<WxworkApp> page = wxworkAppRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> {
                    if (query.getBizId() != null)
                        return criteriaBuilder.equal(root.get("businessSystem").get("id"), query.getBizId());
                    return null;
                }
//                (root, query1, criteriaBuilder) -> {
//                    if (query.getNameLike() != null)
//                        return criteriaBuilder.like(root.get("name"), "%" + query.getNameLike() + "%");
//                    return null;
//                }
        ), pageRequest);

//        Page<WxworkApp> page = wxworkAppRepository.findAll(pageRequest);

        return new PageVo<>(page.getContent().stream().map(WxworkAppForm::new).toList(), page.getTotalElements());
    }

    @GetMapping("/{id}")
    public WxworkAppForm get(@PathVariable("id") Long id) {
        Optional<WxworkApp> item = wxworkAppRepository.findById(id);
        return new WxworkAppForm(item.orElseThrow());
    }

    @PostMapping("operation")
    public void operation(@RequestBody WxworkAppForm form) {
        WxworkApp entity;
        if (form.getId() != null) {
            entity = wxworkAppRepository.findById(form.getId()).orElseThrow();
            entity.setUpdatedAt(LocalDateTime.now());
        } else {
            entity = new WxworkApp();
            entity.setCreatedAt(LocalDateTime.now());

            UnifiedAuthUtil.checkTrue(form.getBizId() != null, "bizId is required");
            BusinessSystem businessSystem = new BusinessSystem();
            businessSystem.setId(form.getBizId());
            entity.setBusinessSystem(businessSystem);
        }

        PlatformUtil.appOperationCommonCheck(entity, form);

        entity.setCorpId(form.getCorpId());
        entity.setAgentId(form.getAgentId());
        entity.setSecret(form.getSecret());
        wxworkAppRepository.save(entity);
    }

    @PostMapping("delete")
    public void delete(@RequestParam Long[] ids) {
        wxworkAppRepository.deleteAllById(List.of(ids));
    }
}
