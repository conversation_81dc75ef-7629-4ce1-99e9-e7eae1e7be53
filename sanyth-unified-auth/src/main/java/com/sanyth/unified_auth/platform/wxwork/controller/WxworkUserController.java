package com.sanyth.unified_auth.platform.wxwork.controller;

import com.sanyth.unified_auth.common.query.QuerySuper;
import com.sanyth.unified_auth.common.util.UnifiedAuthUtil;
import com.sanyth.unified_auth.common.vo.PageVo;
import com.sanyth.unified_auth.platform.wxwork.entity.WxworkApp;
import com.sanyth.unified_auth.platform.wxwork.entity.WxworkUser;
import com.sanyth.unified_auth.platform.wxwork.form.WxworkUserForm;
import com.sanyth.unified_auth.platform.wxwork.repository.WxworkUserRepository;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * @since 2025/6/10 9:33
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/unified-auth/admin/wxwork/user")
public class WxworkUserController {

    private final WxworkUserRepository userRepository;

    @GetMapping("page")
    public PageVo<WxworkUserForm> page(QuerySuper query, WxworkUserForm form) {
        Long appId = form.getAppId();
        UnifiedAuthUtil.checkTrue(appId != null, "appId is required");
        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("createdAt").descending());

        Page<WxworkUser> page = userRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> criteriaBuilder.equal(root.get("app").get("id"), appId),
                (root, query1, criteriaBuilder) -> {
                    if (StringUtils.hasText(form.getBizUserId()))
                        return criteriaBuilder.like(root.get("bizUserId"), "%" + form.getBizUserId() + "%");
                    return null;
                },
                (root, query1, criteriaBuilder) -> {
                    if (StringUtils.hasText(form.getUserId()))
                        return criteriaBuilder.like(root.get("userId"), "%" + form.getUserId() + "%");
                    return null;
                }
        ), pageRequest);

//        Page<WxworkUser> page = WxworkUserRepository.findAll(pageRequest);

        return new PageVo<>(page.getContent().stream().map(WxworkUserForm::new).toList(), page.getTotalElements());
    }

    @GetMapping("/{id}")
    public WxworkUserForm get(@PathVariable("id") Long id) {
        Optional<WxworkUser> item = userRepository.findById(id);
        return new WxworkUserForm(item.orElseThrow());
    }

    @PostMapping("operation")
    public void operation(@RequestBody WxworkUserForm form) {
        WxworkUser entity;
        if (form.getId() != null) {
            entity = userRepository.findById(form.getId()).orElseThrow();
            entity.setUpdatedAt(LocalDateTime.now());
        } else {
            entity = new WxworkUser();
            entity.setCreatedAt(LocalDateTime.now());

            UnifiedAuthUtil.checkTrue(form.getAppId() != null, "appId is required");
            WxworkApp app = new WxworkApp();
            app.setId(form.getAppId());
            entity.setApp(app);
        }

        UnifiedAuthUtil.checkTrue(StringUtils.hasText(form.getUserId()), "业务用户ID不能为空");
        entity.setUserId(form.getUserId());
        entity.setBizUserId(form.getBizUserId());
        userRepository.save(entity);
    }

    @PostMapping("remove")
    public void remove(@RequestBody Long[] ids) {
        userRepository.deleteAllById(List.of(ids));
    }
}
