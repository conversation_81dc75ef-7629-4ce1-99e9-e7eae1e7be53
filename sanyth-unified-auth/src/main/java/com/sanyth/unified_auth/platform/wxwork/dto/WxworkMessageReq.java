package com.sanyth.unified_auth.platform.wxwork.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Collection;

/**
 * @since 2025/6/25 15:56
 */
@Data
public class WxworkMessageReq {

    //{
    //   "touser" : "UserID1|UserID2|UserID3",
    //   "toparty" : "PartyID1|PartyID2",
    //   "totag" : "TagID1 | TagID2",
    //   "msgtype" : "text",
    //   "agentid" : 1,
    //   "text" : {
    //       "content" : "你的快递已到，请携带工卡前往邮件中心领取。\n出发前可查看<a href=\"https://work.weixin.qq.com\">邮件中心视频实况</a>，聪明避开排队。"
    //   },
    //   "safe":0,
    //   "enable_id_trans": 0,
    //   "enable_duplicate_check": 0,
    //   "duplicate_check_interval": 1800
    //}

    public WxworkMessageReq(String agentId, Collection<String> userIds) {
        this.msgtype = "text";
        this.agentId = agentId;
        this.touser = String.join("|", userIds);
    }

    public void setText(WxworkMessageText text) {
        this.text = text;
        this.msgtype = "text";
    }

    public void setTextCard(WxworkMessageTextCard textCard) {
        this.textCard = textCard;
        this.msgtype = "textcard";
    }

    @JsonProperty("agentid")
    private String agentId;
    private String msgtype;
    private String touser;
    private WxworkMessageText text;
    @JsonProperty("textcard")
    private WxworkMessageTextCard textCard;

}
