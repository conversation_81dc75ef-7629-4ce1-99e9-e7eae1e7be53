package com.sanyth.unified_auth.platform.wxwork.entity;

import com.sanyth.unified_auth.platform.entity.PlatformAppSuper;
import jakarta.persistence.CascadeType;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.OneToMany;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 企业微信应用
 *
 * @since 2025/6/4 16:53
 */
@EqualsAndHashCode(callSuper = true)
@Data
//@Table(name = "wxwork_app")
@Entity
@DiscriminatorValue("WXWORK")
public class WxworkApp extends PlatformAppSuper {

    private String corpId;
    private String agentId;
    private String secret;
    @OneToMany(mappedBy = "app", cascade = CascadeType.REMOVE)
    private List<WxworkUser> users;

}
