package com.sanyth.unified_auth.platform.wxwork.form;

import com.sanyth.unified_auth.platform.form.PlatformAppForm;
import com.sanyth.unified_auth.platform.wxwork.entity.WxworkApp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @since 2025/6/10 9:17
 */
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class WxworkAppForm extends PlatformAppForm {

    public WxworkAppForm(WxworkApp entity) {
        super(entity);
        this.corpId = entity.getCorpId();
        this.agentId = entity.getAgentId();
        this.secret = entity.getSecret();
    }

    private String corpId;
    private String agentId;
    private String secret;

}
