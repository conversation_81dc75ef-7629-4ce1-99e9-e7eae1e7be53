package com.sanyth.unified_auth.platform.wxwork.form;

import com.sanyth.unified_auth.common.form.FormSuper;
import com.sanyth.unified_auth.platform.wxwork.entity.WxworkUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @since 2025/6/10 9:34
 */
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class WxworkUserForm extends FormSuper {

    public WxworkUserForm(WxworkUser entity) {
        super(entity);
        this.userId = entity.getUserId();
        this.bizUserId = entity.getBizUserId();
    }

    private Long appId;
    private String userId;
    private String bizUserId;

}
