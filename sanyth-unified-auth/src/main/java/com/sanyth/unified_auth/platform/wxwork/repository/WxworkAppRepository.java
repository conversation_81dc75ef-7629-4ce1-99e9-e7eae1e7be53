package com.sanyth.unified_auth.platform.wxwork.repository;

import com.sanyth.unified_auth.platform.wxwork.entity.WxworkApp;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

public interface WxworkAppRepository extends PagingAndSortingRepository<WxworkApp, Long>, CrudRepository<WxworkApp, Long>, JpaSpecificationExecutor<WxworkApp> {

    WxworkApp findFirstByBusinessSystemId(Long businessSystemId);
}
