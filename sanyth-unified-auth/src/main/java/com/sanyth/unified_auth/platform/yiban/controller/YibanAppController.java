package com.sanyth.unified_auth.platform.yiban.controller;

import com.sanyth.unified_auth.common.entity.BusinessSystem;
import com.sanyth.unified_auth.common.error.RequestError;
import com.sanyth.unified_auth.common.query.QuerySuper;
import com.sanyth.unified_auth.common.util.UnifiedAuthUtil;
import com.sanyth.unified_auth.common.vo.PageVo;
import com.sanyth.unified_auth.platform.util.PlatformUtil;
import com.sanyth.unified_auth.platform.yiban.entity.YibanApp;
import com.sanyth.unified_auth.platform.yiban.form.YibanAppForm;
import com.sanyth.unified_auth.platform.yiban.repository.YibanAppRepository;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * @since 2025/7/8 15:44
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/unified-auth/admin/yiban/app")
public class YibanAppController {

    private final YibanAppRepository appRepository;

    @GetMapping("page")
    public PageVo<YibanAppForm> page(QuerySuper query, Long bizId) {
        UnifiedAuthUtil.checkTrue(bizId != null, "bizId is required");

        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("createdAt").descending());

        Page<YibanApp> page = appRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder)
                        -> criteriaBuilder.equal(root.get("businessSystem").get("id"), bizId)
        ), pageRequest);

        return new PageVo<>(page.getContent().stream().map(YibanAppForm::new).toList(), page.getTotalElements());
    }

    @GetMapping("/{id}")
    public YibanAppForm get(@PathVariable("id") Long id) {
        Optional<YibanApp> item = appRepository.findById(id);
        return new YibanAppForm(item.orElseThrow());
    }

    @PostMapping("operation")
    public void operation(@RequestBody YibanAppForm form) {
        YibanApp entity;
        if (form.getId() != null) {
            entity = appRepository.findById(form.getId()).orElseThrow();
            entity.setUpdatedAt(LocalDateTime.now());
        } else {
            entity = new YibanApp();
            entity.setCreatedAt(LocalDateTime.now());

            RequestError.isTrue(form.getBizId() != null, "bizId is required");
            BusinessSystem businessSystem = new BusinessSystem();
            businessSystem.setId(form.getBizId());
            entity.setBusinessSystem(businessSystem);
        }

        PlatformUtil.appOperationCommonCheck(entity, form);

        entity.setAppId(form.getAppId());
        entity.setAppSecret(form.getAppSecret());
        appRepository.save(entity);
    }

    @PostMapping("delete")
    public void delete(@RequestParam Long[] ids) {
        appRepository.deleteAllById(List.of(ids));
    }
}
