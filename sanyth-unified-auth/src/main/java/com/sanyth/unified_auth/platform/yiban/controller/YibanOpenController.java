package com.sanyth.unified_auth.platform.yiban.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sanyth.core.annotation.IgnoreResponseBodyAdvice;
import com.sanyth.unified_auth.common.error.RequestError;
import com.sanyth.unified_auth.connection.service.ConnOpenService;
import com.sanyth.unified_auth.platform.yiban.dto.YibanVerifyRequest;
import com.sanyth.unified_auth.platform.yiban.entity.YibanApp;
import com.sanyth.unified_auth.platform.yiban.entity.YibanUser;
import com.sanyth.unified_auth.platform.yiban.repository.YibanAppRepository;
import com.sanyth.unified_auth.platform.yiban.repository.YibanUserRepository;
import com.sanyth.unified_auth.platform.yiban.util.YibanUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @since 2025/7/9 9:17
 */
@Slf4j
@IgnoreResponseBodyAdvice
@AllArgsConstructor
@Controller
@RequestMapping("/unified-auth/open/yiban")
public class YibanOpenController {

    private final ConnOpenService connOpenService;
    private final YibanAppRepository appRepository;
    private final YibanUserRepository userRepository;

    @ExceptionHandler(Exception.class)
    public ResponseEntity<String> handleException(Exception ex) {
        return ResponseEntity.status(HttpStatus.OK).body("系统异常：" + ex.getMessage());
    }

    @ExceptionHandler(RequestError.class)
    public ResponseEntity<String> handleRequestError(RequestError ex) {
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(ex.getContent());
    }

    @GetMapping("auth/{id}")
    public String auth(@PathVariable String id, HttpServletRequest request, String inner_param, String verify_request) {
        log.debug("id: {}", id);
        log.debug("verify_request: {}", verify_request);

//        YibanApp app = appRepository.findById(id).orElseThrow();
//        String redirectUri = UriComponentsBuilder.fromUriString(PlatformUtil.getUrlRoot(request))
//                .path("/unified-auth/open/wxwork/callback").queryParam("id", id).queryParam("inner_param", inner_param).build().toUriString();
//
//        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString("https://open.weixin.qq.com/connect/oauth2/authorize");
//        uriComponentsBuilder.queryParam("appid", app.getCorpId());
//        uriComponentsBuilder.queryParam("redirect_uri", PlatformUtil.urlEncode(redirectUri));
//        uriComponentsBuilder.queryParam("response_type", "code");
//        uriComponentsBuilder.queryParam("scope", "snsapi_base");
//        uriComponentsBuilder.queryParam("state", "STATE");
//        uriComponentsBuilder.queryParam("agentid", app.getAgentId());
//        uriComponentsBuilder.fragment("wechat_redirect");
//        URI uri = uriComponentsBuilder.build().toUri();
////        System.out.println(uri);
//
//        return "redirect:" + uri;

        Long idLong = Long.valueOf(id);
        YibanApp app = appRepository.findById(idLong).orElseThrow();
        String decrypted = YibanUtil.decryptVerifyRequest(verify_request, app.getAppSecret(), app.getAppId());
        log.debug("decrypted: {}", decrypted);

        ObjectMapper objectMapper = new ObjectMapper();
        YibanVerifyRequest verifyRequest = null;
        try {
            verifyRequest = objectMapper.readValue(decrypted, YibanVerifyRequest.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
//        RequestError.isTrue(verifyRequest.getVisit_user() != null, "visit_user is null");
        String userid = verifyRequest.getVisit_user().getUserid();
        YibanUser user = userRepository.findFirstByAppAndUserId(app, userid);
        RequestError.isTrue(user != null, "user not found, your yiban_userid: " + userid);

        return "redirect:" + connOpenService.checkUsername(app.getBusinessSystem(), user.getBizUserId(), idLong, inner_param);
    }

    @GetMapping("callback")
    public void callback(String code, Long id, String inner_param) {
//        WxworkApp app = appRepository.findById(id).orElseThrow();
//        String accessToken = apiClient.getAccessToken(app);
//        String userId = apiClient.getUserId(accessToken, code);
//        String username = userId;
//
//        // 获取用户映射
//        WxworkUser user = userRepository.findFirstByUserId(userId);
//        if (user != null) {
//            username = user.getBizUserId();
//        }
//
//        return "redirect:" + connOpenService.checkUsername(app.getBusinessSystem(), username, id, inner_param);

    }
}
