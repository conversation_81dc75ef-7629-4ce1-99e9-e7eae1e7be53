package com.sanyth.unified_auth.platform.yiban.controller;

import com.sanyth.unified_auth.common.query.QuerySuper;
import com.sanyth.unified_auth.common.util.UnifiedAuthUtil;
import com.sanyth.unified_auth.common.vo.PageVo;
import com.sanyth.unified_auth.platform.yiban.entity.YibanApp;
import com.sanyth.unified_auth.platform.yiban.entity.YibanUser;
import com.sanyth.unified_auth.platform.yiban.form.YibanUserForm;
import com.sanyth.unified_auth.platform.yiban.repository.YibanUserRepository;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * @since 2025/7/8 16:05
 */
@AllArgsConstructor
@RestController
@RequestMapping("/api/unified-auth/admin/yiban/user")
public class YibanUserController {

    private final YibanUserRepository userRepository;

    @GetMapping("page")
    public PageVo<YibanUserForm> page(QuerySuper query, YibanUserForm form) {
        Long appId = form.getAppId();
        UnifiedAuthUtil.checkTrue(appId != null, "appId is required");

        PageRequest pageRequest = PageRequest.of(query.getPageNumber(), query.getPageSize(), Sort.by("createdAt").descending());

        Page<YibanUser> page = userRepository.findAll(Specification.allOf(
                (root, query1, criteriaBuilder) -> criteriaBuilder.equal(root.get("app").get("id"), appId),
                (root, query1, criteriaBuilder) -> {
                    if (!StringUtils.hasText(form.getBizUserId())) {
                        return null;
                    }
                   return criteriaBuilder.like(root.get("bizUserId"), "%" + form.getBizUserId() + "%");
                },
                (root, query1, criteriaBuilder) -> {
                    if (!StringUtils.hasText(form.getUserId())) {
                        return null;
                    }
                    return criteriaBuilder.like(root.get("userId"), "%" + form.getUserId() + "%");
                },
                (root, query1, criteriaBuilder) -> {
                    if (!StringUtils.hasText(form.getUsername())) {
                        return null;
                    }
                    return criteriaBuilder.like(root.get("username"), "%" + form.getUsername() + "%");
                },
                (root, query1, criteriaBuilder) -> {
                    if (form.getTester() == null) {
                        return null;
                    }
                    return criteriaBuilder.equal(root.get("tester"), form.getTester());
                }
        ), pageRequest);

        return new PageVo<>(page.getContent().stream().map(YibanUserForm::new).toList(), page.getTotalElements());
    }

    @GetMapping("/{id}")
    public YibanUserForm get(@PathVariable("id") Long id) {
        Optional<YibanUser> item = userRepository.findById(id);
        return new YibanUserForm(item.orElseThrow());
    }

    @PostMapping("operation")
    public void operation(@RequestBody YibanUserForm form) {
        YibanUser entity;
        if (form.getId() != null) {
            entity = userRepository.findById(form.getId()).orElseThrow();
            entity.setUpdatedAt(LocalDateTime.now());
        } else {
            entity = new YibanUser();
            entity.setCreatedAt(LocalDateTime.now());

            UnifiedAuthUtil.checkTrue(form.getAppId() != null, "appId is required");
            YibanApp app = new YibanApp();
            app.setId(form.getAppId());
            entity.setApp(app);
        }

        UnifiedAuthUtil.checkTrue(StringUtils.hasText(form.getBizUserId()), "业务用户ID不能为空");
        entity.setUserId(form.getBizUserId());
        entity.setUsername(form.getUsername());
        entity.setBizUserId(form.getBizUserId());
        entity.setTester(form.getTester());
        if (entity.getTester() == null) {
            entity.setTester(false);
        }
        userRepository.save(entity);
    }

    @PostMapping("remove")
    public void remove(@RequestBody Long[] ids) {
        userRepository.deleteAllById(List.of(ids));
    }
}
