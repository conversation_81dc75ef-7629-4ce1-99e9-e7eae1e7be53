package com.sanyth.unified_auth.platform.yiban.entity;

import com.sanyth.unified_auth.platform.entity.PlatformAppSuper;
import jakarta.persistence.CascadeType;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.OneToMany;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 易班应用
 *
 * @since 2025/7/8 15:28
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@DiscriminatorValue("YIBAN")
public class YibanApp extends PlatformAppSuper {

    private String appId;
    private String appSecret;
    @OneToMany(mappedBy = "app", cascade = CascadeType.REMOVE)
    private List<YibanUser> users;

}
