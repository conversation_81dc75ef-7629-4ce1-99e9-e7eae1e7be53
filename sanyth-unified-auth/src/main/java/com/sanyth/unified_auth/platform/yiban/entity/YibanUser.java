package com.sanyth.unified_auth.platform.yiban.entity;

import com.sanyth.unified_auth.common.entity.EntitySuper;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.ColumnDefault;

/**
 * @since 2025/7/8 15:35
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "unified_auth_platform_yiban_user")
public class YibanUser extends EntitySuper {

    @ManyToOne
    private YibanApp app;
    private String userId;
    private String username;
    private String bizUserId;
    @Column(nullable = false)
    @ColumnDefault("0")
    private Boolean tester;

}
