package com.sanyth.unified_auth.platform.yiban.form;

import com.sanyth.unified_auth.platform.form.PlatformAppForm;
import com.sanyth.unified_auth.platform.yiban.entity.YibanApp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @since 2025/7/8 15:46
 */
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class YibanAppForm extends PlatformAppForm {

    public YibanAppForm(YibanApp entity) {
        super(entity);
        this.appId = entity.getAppId();
        this.appSecret = entity.getAppSecret();
    }

    private String appId;
    private String appSecret;

}
