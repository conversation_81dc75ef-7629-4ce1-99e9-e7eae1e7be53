package com.sanyth.unified_auth.platform.yiban.form;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sanyth.unified_auth.common.form.FormSuper;
import com.sanyth.unified_auth.common.util.UcBooleanToCnSerializer;
import com.sanyth.unified_auth.common.util.UcCnToBooleanDeserializer;
import com.sanyth.unified_auth.platform.yiban.entity.YibanUser;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @since 2025/7/8 15:46
 */
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
public class YibanUserForm extends FormSuper {

    public YibanUserForm(YibanUser entity) {
        super(entity);
        this.username = entity.getUsername();
        this.userId = entity.getUserId();
        this.bizUserId = entity.getBizUserId();
        this.tester = entity.getTester();
    }

    private Long appId;
    private String userId;
    private String username;
    private String bizUserId;
    @JsonSerialize(using = UcBooleanToCnSerializer.class)
    @JsonDeserialize(using = UcCnToBooleanDeserializer.class)
    private Boolean tester;

}
