package com.sanyth.unified_auth.platform.yiban.repository;

import com.sanyth.unified_auth.platform.yiban.entity.YibanApp;
import com.sanyth.unified_auth.platform.yiban.entity.YibanUser;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.Collection;
import java.util.List;

public interface YibanUserRepository extends PagingAndSortingRepository<YibanUser, Long>, CrudRepository<YibanUser, Long>, JpaSpecificationExecutor<YibanUser> {
    YibanUser findFirstByAppAndUserId(YibanApp app, String userId);

    List<YibanUser> findAllByAppAndBizUserIdIn(YibanApp app, Collection<String> bizUserIds);
}
