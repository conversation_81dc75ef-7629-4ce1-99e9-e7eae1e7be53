<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title>绑定管理</title>
    <link rel="stylesheet" href="/unified-auth/open/lib/platform/wechat/css/weui.min.css">
    <link rel="stylesheet" href="/unified-auth/open/lib/platform/wechat/css/weui_example.css">
    <link rel="stylesheet" href="/unified-auth/open/lib/platform/wechat/css/index.css">
</head>
<body>
<div class="container">
    <div class="page msg_text_primary js_show">
        <div class="weui-msg">
            <template th:if="${empty username}">
                <div class="weui-msg__icon-area">
                    <img class="header-img"
                         src="/unified-auth/open/lib/platform/wechat/img/bg3.png"
                         alt=""/>
                </div>
                <div class="weui-msg__text-area">
                    <h2 class="weui-msg__title">未绑定</h2>
                </div>
                <div class="weui-msg__opr-area">
                    <p class="weui-btn-area">
                        <a th:href="@{/unified-auth/open/wechat/bind_page}"
                           role="button" class="weui-btn weui-btn_primary">验证账号并绑定</a>
                    </p>
                </div>
                <div class="weui-msg__tips-area">
                    <p class="weui-msg__tips">首次验证并绑定账号，可方便您下次免登录访问本服务，也可随时解绑。</p>
                </div>
            </template>
            <template th:if="${not empty username}">
                <div class="weui-msg__icon-area">
                    <img class="header-img"
                         src="/unified-auth/open/lib/platform/wechat/img/bg2.png"
                         alt=""/>
                </div>
                <div class="weui-msg__text-area">
                    <h2 class="weui-msg__title">已绑定</h2>
                    <p class="weui-msg__desc-primary">用户名：<span style="color: #2D8CF0;" th:text="${username}"></span></p>
                </div>
                <div class="weui-msg__opr-area">
                    <p class="weui-btn-area">
                        <a href="/unified-auth/open/wechat/to_biz"
                           role="button" class="weui-btn weui-btn_primary">进入系统</a>
                        <a href="javascript:onRevoke()"
                           role="button" class="weui-btn weui-btn_default">解除绑定</a>
                    </p>
                </div>
            </template>
            <div class="weui-msg__extra-area">
                <div class="weui-footer">
                    <p class="weui-footer__text" th:text="${openId}"></p>
                </div>
            </div>
        </div>

        <div id="dialogs" wah-hotarea="click">
            <div class="js_dialog" role="dialog" aria-hidden="false" aria-modal="true" aria-labelledby="js_title1"
                 id="iosDialog1" style="opacity: 1;display: none;" tabindex="0">
                <div class="weui-mask"></div>
                <div class="weui-dialog">
                    <div class="weui-dialog__bd">确认解除绑定？</div>
                    <div class="weui-dialog__ft">
                        <a role="button"
                           href="${pageContext.request.contextPath}/nonlogin/syt/platform/weixin/webAuth/unbindResult.htm"
                           class="weui-dialog__btn weui-dialog__btn_warn">是</a>
                        <a role="button" href="javascript:onRevokeCancel()"
                           class="weui-dialog__btn weui-dialog__btn_primary">否</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    function onRevoke() {
        document.querySelector("#iosDialog1").style.display = "block";
    }

    function onRevokeCancel() {
        document.querySelector("#iosDialog1").style.display = "none";
    }
</script>
</body>
</html>