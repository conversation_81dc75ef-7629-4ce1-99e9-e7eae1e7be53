package com.sanyth.upms.code.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sanyth.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 学院代码
 *
 * <AUTHOR>
 * @since 2024-03-11 15:28:15
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYT_CODE_DWB")
@Table(name = "SYT_CODE_DWB")
public class CodeDwb implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @ExcelIgnore
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 备注
     */
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "备注", index = 4)
    @Column(name = "BZ")
    @TableField("BZ")
    private String bz;

    /**
     * 学院代码
     */
    @NotNull(message = "学院代码不能为空")
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "学院代码", index = 1)
    @Column(name = "CODE")
    @TableField("CODE")
    private String code;

    /**
     * 学院名称
     */
    @NotNull(message = "学院名称不能为空")
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "学院名称", index = 0)
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 是否教学单位
     */
    @NotNull(message = "是否教学单位不能为空")
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "是否教学单位", index = 2)
    @Column(name = "SFJXDW")
    @TableField("SFJXDW")
    private String sfjxdw;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序", index = 3)
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

}
