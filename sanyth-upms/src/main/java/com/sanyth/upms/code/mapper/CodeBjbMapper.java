package com.sanyth.upms.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanyth.upms.code.entity.CodeBjb;
import com.sanyth.upms.code.param.CodeBjbParam;
import com.sanyth.upms.code.result.CodeBjbResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 班级代码Mapper
 *
 * <AUTHOR>
 * @since 2024-03-12 13:53:31
 */
public interface CodeBjbMapper extends BaseMapper<CodeBjb> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<CodeBjb>
     */
    List<CodeBjbResult> selectPageRel(@Param("page") IPage<CodeBjb> page,
                                      @Param("param") CodeBjbParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<CodeBjb> selectListRel(@Param("param") CodeBjbParam param);

}
