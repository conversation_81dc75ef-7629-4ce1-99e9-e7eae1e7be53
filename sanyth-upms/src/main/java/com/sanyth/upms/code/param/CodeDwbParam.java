package com.sanyth.upms.code.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanyth.core.annotation.QueryField;
import com.sanyth.core.annotation.QueryType;
import com.sanyth.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 学院代码查询参数
 *
 * <AUTHOR>
 * @since 2024-03-11 15:28:15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CodeDwbParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 学院ID
     */
    @QueryField(type = QueryType.IN_STR)
    private String id;

    /**
     * 备注
     */
    private String bz;

    /**
     * 学院代码
     */
    private String code;

    /**
     * 学院名称
     */
    private String name;

    /**
     * 是否教学单位
     */
    @QueryField(type = QueryType.EQ)
    private String sfjxdw;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort desc";
    }
}
