package com.sanyth.upms.system.controller;

import com.sanyth.core.annotation.OperationLog;
import com.sanyth.core.web.BaseController;
import com.sanyth.core.web.PageParam;
import com.sanyth.core.web.PageResult;
import com.sanyth.upms.code.entity.CodeBjb;
import com.sanyth.upms.code.entity.CodeZyb;
import com.sanyth.upms.code.param.CodeBjbParam;
import com.sanyth.upms.code.param.CodeZybParam;
import com.sanyth.upms.code.service.CodeBjbService;
import com.sanyth.upms.code.service.CodeZybService;
import com.sanyth.upms.system.entity.UserDataScope;
import com.sanyth.upms.system.param.UserDataScopeParam;
import com.sanyth.upms.system.service.UserDataScopeService;
import com.sanyth.upms.system.vo.UserDataScopeVO;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户数据权限控制器
 *
 * <AUTHOR>
 * @since 2024-04-22 13:37:34
 */
@RestController
@RequestMapping("/api/userInfo/userDataScope")
public class UserDataScopeController extends BaseController {
    @Resource
    private UserDataScopeService userDataScopeService;
    @Resource
    private CodeBjbService codeBjbService;
    @Resource
    private CodeZybService codeZybService;

    /**
     * 分页查询用户数据权限（权限标识：userInfo:userDataScope:list）
     */
    @PreAuthorize("hasAuthority('userInfo:userDataScope:list')")
    @GetMapping("/page")
    public PageResult<UserDataScope> page(UserDataScopeParam param) {
        PageParam<UserDataScope, UserDataScopeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = userDataScopeService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部用户数据权限（权限标识：userInfo:userDataScope:list）
     */
    @PreAuthorize("hasAuthority('userInfo:userDataScope:list')")
    @GetMapping()
    public List<UserDataScope> list(UserDataScopeParam param) {
        return userDataScopeService.queryList(param);
    }

    /**
     * 添加或修改用户数据权限（权限标识：userInfo:userDataScope:operation）
     */
    @PreAuthorize("hasAuthority('userInfo:userDataScope:operation')")
    @OperationLog(module = "用户数据权限", comments = "保存用户数据权限")
    @PostMapping("/operation")
    public void save(@Validated @RequestBody UserDataScope userDataScope) {
        userDataScopeService.edit(userDataScope);
    }

    /**
     * 批量删除用户数据权限（权限标识：userInfo:userDataScope:remove）
     */
    @PreAuthorize("hasAuthority('userInfo:userDataScope:remove')")
    @OperationLog(module = "用户数据权限", comments = "批量删除用户数据权限")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        userDataScopeService.removeByIds(ids);
    }

    /**
     * 获取用户数据权限（权限标识：userInfo:userDataScope:get）
     */
    @PreAuthorize("hasAuthority('userInfo:userDataScope:get')")
    @GetMapping("/{glzXgh}/{roleId}")
    public UserDataScope get(@PathVariable("glzXgh") String glzXgh, @PathVariable("roleId") String roleId) {
        UserDataScopeParam param = new UserDataScopeParam();
        param.setGlzXgh(glzXgh);
        param.setRoleId(roleId);
        List<UserDataScope> list = userDataScopeService.queryList(param);
        UserDataScope dataScope = userDataScopeService.list2Single(list, true);
        UserDataScopeVO userDataScopeVO = new UserDataScopeVO();
        BeanUtils.copyProperties(dataScope, userDataScopeVO);
        if (StringUtils.hasText(dataScope.getBjid())) {
            CodeBjbParam codeBjbParam = new CodeBjbParam();
            codeBjbParam.setId(dataScope.getBjid());
            List<CodeBjb> codeBjbs = codeBjbService.list(codeBjbParam);
            StringBuilder id = new StringBuilder();
            StringBuilder name = new StringBuilder();
            for (CodeBjb codeBjb : codeBjbs) {
                id.append(codeBjb.getId()).append(",");
                name.append(codeBjb.getName()).append(",");
            }
            id.deleteCharAt(id.length() - 1);
            name.deleteCharAt(name.length() - 1);
            userDataScopeVO.setBjid(id.toString());
            userDataScopeVO.setBjmc(name.toString());
        }

        if (StringUtils.hasText(dataScope.getZyid())) {
            CodeZybParam codeZybParam = new CodeZybParam();
            codeZybParam.setId(dataScope.getZyid());
            List<CodeZyb> codeZybs = codeZybService.list(codeZybParam);
            StringBuilder id = new StringBuilder();
            StringBuilder name = new StringBuilder();
            for (CodeZyb codeZyb : codeZybs) {
                id.append(codeZyb.getId()).append(",");
                name.append(codeZyb.getName()).append(",");
            }
            id.deleteCharAt(id.length() - 1);
            name.deleteCharAt(name.length() - 1);
            userDataScopeVO.setZyid(id.toString());
            userDataScopeVO.setZymc(name.toString());
        }
        return userDataScopeVO;
    }
}
