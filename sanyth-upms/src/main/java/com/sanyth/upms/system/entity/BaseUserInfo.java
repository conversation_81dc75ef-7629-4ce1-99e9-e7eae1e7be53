package com.sanyth.upms.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.sanyth.core.enums.Gender;
import com.sanyth.core.enums.UserType;
import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Transient;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;

/**
 * Created by JIANGPING on 2024/7/15.
 */
@Data
@MappedSuperclass
public class BaseUserInfo implements Serializable {
    /**
     * 学号/工号，对应账户表username
     */
    @Id
    @Column(name = "XGH", columnDefinition = "VARCHAR2(50)")
    @TableId(value = "XGH", type = IdType.ASSIGN_UUID)
    private String xgh;

    /**
     * 姓名
     */
    @Column(name = "XM")
    @TableField("XM")
    private String xm;

    /**
     * 性别（男：1， 女：2）
     */
    @Column(name = "XB", columnDefinition = "NUMBER(1)")
    @TableField(value = "XB", jdbcType = JdbcType.INTEGER)
    private Gender xb;

    /**
     * 院系ID
     */
    @Column(name = "XYID")
    @TableField("XYID")
    private String xyid;

    /**
     * 专业ID
     */
    @Column(name = "ZYID")
    @TableField("ZYID")
    private String zyid;

    /**
     * 班级ID
     */
    @Column(name = "BJID")
    @TableField("BJID")
    private String bjid;

    /**
     * 年级ID
     */
    @Column(name = "NJID")
    @TableField("NJID")
    private String njid;

    /**
     * 手机号
     */
    @Column(name = "SJH")
    @TableField("SJH")
    private String sjh;

    /**
     * 出生日期
     */
    @Column(name = "CSRQ")
    @TableField("CSRQ")
    private String csrq;

    /**
     * 籍贯
     */
    @Column(name = "JG")
    @TableField("JG")
    private String jg;

    /**
     * 民族
     */
    @Column(name = "MZMC")
    @TableField("MZMC")
    private String mzmc;

    /**
     * 培养层次ID
     */
    @Column(name = "PYCCID")
    @TableField("PYCCID")
    private String pyccid;

    /**
     * 政治面貌
     */
    @Column(name = "ZZMMMC")
    @TableField("ZZMMMC")
    private String zzmmmc;

    /**
     * 校区名称
     */
    @Column(name = "XQMC")
    @TableField("XQMC")
    private String xqmc;

    /**
     * 证件号码
     */
//    @FieldMask(value = Mask.ID_CARD)
//    @EncryptedColumn
    @Column(name = "ZJHM")
    @TableField("ZJHM")
    private String zjhm;

    /**
     * 证件类型
     */
    @Column(name = "ZJLX")
    @TableField("ZJLX")
    private String zjlx;

    /**
     * 学生类别（本，硕，博，进修生，留学生等）
     */
    @Column(name = "XSLB")
    @TableField("XSLB")
    private String xslb;

    /**
     * 学制类型（三年制，四年制，五年制等）
     */
    @Column(name = "XZLX")
    @TableField("XZLX")
    private String xzlx;

    /**
     * 人员状态ID
     */
    @Column(name = "RYZTID")
    @TableField("RYZTID")
    private String ryztid;
    /**
     * 角色
     */
    @Column(name = "ROLE_ID")
    @TableField(value = "ROLE_ID")
    private String roleId;
    /**
     * 用户类别
     */
    @Column(name = "USER_TYPE", columnDefinition = "NUMBER(1)")
    @TableField(value = "USER_TYPE", jdbcType = JdbcType.INTEGER)
    private UserType userType;
    /**
     * 照片信息
     */
    @Column(name = "PHOTO")
    @TableField(value = "PHOTO")
    private String photo;
    /**
     * 删除标识
     */
    @Column(name = "DELETED", columnDefinition = "NUMBER(1) DEFAULT 0")
    @TableLogic
    private Integer deleted;


    // 扩展临时字段

    /**
     * 学院名称
     */
    @TableField(exist = false)
    @Transient
    private String xymc;
    /**
     * 专业名称
     */
    @TableField(exist = false)
    @Transient
    private String zymc;
    /**
     * 班级名称
     */
    @TableField(exist = false)
    @Transient
    private String bjmc;
    /**
     * 年级名称
     */
    @TableField(exist = false)
    @Transient
    private String njmc;
    /**
     * 培养层次名称
     */
    @TableField(exist = false)
    @Transient
    private String pyccmc;
    /**
     * 人员状态
     */
    @TableField(exist = false)
    @Transient
    private String ryzt;
}
