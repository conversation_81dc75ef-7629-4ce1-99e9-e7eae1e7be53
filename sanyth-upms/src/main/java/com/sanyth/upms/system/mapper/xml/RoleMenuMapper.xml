<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sanyth.upms.system.mapper.RoleMenuMapper">

    <!-- 查询用户的菜单 -->
    <select id="listMenuByAccount" resultType="com.sanyth.upms.system.entity.Menu">
        SELECT a.*
        FROM SYT_SYS_MENU a
        <where>
            AND a.menu_id IN (
            SELECT menu_id FROM SYT_SYS_ROLE_MENU WHERE role_id IN (
            SELECT ta.role_id FROM SYT_SYS_ACCOUNT_ROLE ta LEFT JOIN syt_sys_role tb ON ta.role_id = tb.id
            WHERE ta.account_id = #{accountId} and ta.role_id = #{roleId}
            )
            )
            <if test="menuType != null">
                AND a.menu_type = #{menuType}
            </if>
            AND a.deleted = 0
        </where>
        ORDER BY a.sort_number
    </select>

    <!-- 根据角色id查询菜单 -->
    <select id="listMenuByRoleIds" resultType="com.sanyth.upms.system.entity.Menu">
        SELECT a.*
        FROM SYT_SYS_MENU a
        <where>
            AND a.menu_id IN (SELECT menu_id FROM SYT_SYS_ROLE_MENU WHERE role_id IN
            <foreach collection="roleIds" item="roleId" separator="," open="(" close=")">
                #{roleId}
            </foreach>
            )
            <if test="menuType != null">
                AND a.menu_type = #{menuType}
            </if>
            AND a.deleted = 0
        </where>
        ORDER BY a.sort_number
    </select>

</mapper>
