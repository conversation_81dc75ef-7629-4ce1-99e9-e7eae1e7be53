package com.sanyth.upms.system.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sanyth.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 登录日志查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LoginRecordParam extends BaseParam {
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     **/
    private String id;

    /**
     * 用户账号
     **/
    private String username;

    /**
     * 操作系统
     **/
    private String os;

    /**
     * 设备名
     **/
    private String device;

    /**
     * 浏览器类型
     **/
    private String browser;

    /**
     * ip地址
     **/
    private String ip;

    /**
     * 操作类型, 0登录成功, 1登录失败, 2退出登录, 3续签token
     **/
    private Integer loginType;

    /**
     * 备注
     **/
    private String comments;
}
