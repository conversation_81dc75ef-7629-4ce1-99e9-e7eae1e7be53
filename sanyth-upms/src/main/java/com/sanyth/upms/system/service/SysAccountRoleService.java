package com.sanyth.upms.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.core.web.PageResult;
import com.sanyth.upms.system.entity.SysAccountRole;
import com.sanyth.upms.system.entity.SysRole;
import com.sanyth.upms.system.param.SysAccountRoleParam;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 系统用户角色关系Service
 *
 */
public interface SysAccountRoleService extends IService<SysAccountRole> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<SysAccountRole>
     */
    PageResult<SysAccountRole> pageRel(SysAccountRoleParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<SysAccountRole>
     */
    List<SysAccountRole> listRel(SysAccountRoleParam param);

    /**
     * 根据id查询
     *
     * @param id
     * @return SysAccountRole
     */
    SysAccountRole getByIdRel(String id);

    List<SysRole> selectByAccountId(@NotNull String accountId, String roleId);
}
