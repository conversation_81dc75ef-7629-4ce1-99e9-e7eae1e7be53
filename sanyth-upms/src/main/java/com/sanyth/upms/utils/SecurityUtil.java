package com.sanyth.upms.utils;

import com.sanyth.core.utils.SpringContextHolder;
import com.sanyth.upms.system.entity.SysAccount;
import com.sanyth.upms.system.entity.SysRole;
import com.sanyth.upms.system.entity.UserInfo;
import com.sanyth.upms.system.mapper.UserInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

@Slf4j
public class SecurityUtil {

    public static SysAccount getAccount() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()
                    && authentication.getPrincipal() instanceof SysAccount) {
                return (SysAccount) authentication.getPrincipal();
            }
            log.warn("未获取到当前登录用户信息");
            return null;
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return null;
        }
    }

    public static String getUsername() {
        SysAccount account = getAccount();
        return account == null ? null : account.getUsername();
    }

    public static String getRealName() {
        SysAccount account = getAccount();
        return account == null ? null : account.getRealName();
    }

    public static UserInfo getUserInfo() {
        SysAccount account = getAccount();
        if (account != null) {
            UserInfoMapper userInfoMapper = SpringContextHolder.getBean(UserInfoMapper.class);
            return userInfoMapper.selectById(account.getUsername());
        }
        return null;
    }

    public static SysRole getRole() {
        SysAccount account = getAccount();
        return account.getRole();
    }

    public static String getRoleId() {
        SysAccount account = getAccount();
        return account.getRole().getId();
    }

    public static String getRoleScope() {
        SysAccount account = getAccount();
        return account.getRole().getRoleScope();
    }

    public static String getRoleName() {
        SysAccount account = getAccount();
        return account.getRole().getName();
    }
}
