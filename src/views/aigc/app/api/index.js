import request from '@/utils/request';

/**
 * 查询不分页AIGC应用列表
 */
export async function query(params) {
  const res = await request.get('/aigc/aigc-app', { params });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询AIGC应用
 */
export async function queryPage(params) {
  const res = await request.get('/aigc/aigc-app/page', { params });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询AIGC应用
 */
export async function getById(id) {
  const res = await request.get('/aigc/aigc-app/' + id);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改AIGC应用
 */
export async function operation(data) {
  const res = await request.post('/aigc/aigc-app/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除AIGC应用
 */
export async function removes(data) {
  const res = await request.post('/aigc/aigc-app/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
