<template>
  <ele-page flex-table>
    <search @search="reload" />
    <ele-card
      flex-table
      :body-style="{ padding: '0 5px 10px 5px!important', overflow: 'hidden' }"
    >
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        cache-key="sytAigcApp"
      >
        <template #toolbar>
          <el-button
            type="primary"
            size="small"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            type="danger"
            size="small"
            class="ele-btn-icon"
            :icon="DeleteOutlined"
            @click="remove()"
          >
            删除
          </el-button>
        </template>
        <template #action="{ row }">
          <el-link type="primary" :underline="false" @click="openEdit(row)">
            修改
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" :underline="false" @click="remove(row)">
            删除
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" :underline="false" @click="openChat(row)">
            聊天
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" :underline="false" @click="openApiKeys(row)">
            密钥配置
          </el-link>
        </template>
        <!-- 添加状态列的插槽 -->
        <template #status="{ row }">
          <el-tag :type="row.status === '启用' ? 'success' : 'info'">
            {{ row.status === '启用' ? '启用' : '停用' }}
          </el-tag>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <edit v-model="showEdit" :data="current" @done="reload" />
    <!-- 将弹窗改为抽屉 -->
    <el-drawer
      v-model="chatVisible"
      title="聊天"
      size="60%"
      :destroy-on-close="true"
    >
      <Chat
        v-if="chatVisible"
        :app-id="currentApp.id"
        :app-name="currentApp.name"
        :prologue="currentApp.prologue"
      />
    </el-drawer>
  </ele-page>
</template>

<script setup>
  import { reactive, ref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { DeleteOutlined, PlusOutlined } from '@/components/icons';
  import Edit from './components/edit.vue';
  import Search from './components/search.vue';
  import { queryPage, removes } from './api';
  import { query as getModelList } from '../model/api';
  import { query as getKnowledgeList } from '../knowledge/api';
  import { query as getToolList } from '../tool/api';
  import { queryClass } from '../square/api';
  import Chat from './components/chat.vue';
  import { useRouter } from 'vue-router';

  /** 表格实例 */
  const tableRef = ref(null);

  /** 模型列表 */
  const modelOptions = ref([]);

  /** 获取模型列表 */
  const loadModelOptions = async () => {
    try {
      const data = await getModelList();
      modelOptions.value = data.map((item) => ({
        value: item.id,
        label: item.name
      }));
    } catch (e) {
      EleMessage.error('获取模型列表失败');
    }
  };

  /** 初始化加载模型列表 */
  loadModelOptions();

  /** 获取标签显示文本 */
  const getLabelByValue = (value, options) => {
    const option = options.find((item) => item.value === value);
    return option ? option.label : value;
  };

  /** 知识库列表 */
  const knowledgeOptions = ref([]);

  /** 获取知识库列表 */
  const loadKnowledgeOptions = async () => {
    try {
      const data = await getKnowledgeList();
      knowledgeOptions.value = data.map((item) => ({
        value: item.id,
        label: item.name
      }));
    } catch (e) {
      EleMessage.error('获取知识库列表失败');
    }
  };

  /** 初始化加载知识库列表 */
  loadKnowledgeOptions();

  /** 工具列表 */
  const toolOptions = ref([]);

  /** 获取工具列表 */
  const loadToolOptions = async () => {
    try {
      const data = await getToolList({
        status: '0' // 只获取启用状态的工具
      });
      toolOptions.value = data.map((item) => ({
        value: item.id,
        label: item.name
      }));
    } catch (e) {
      EleMessage.error('获取工具列表失败');
    }
  };

  /** 初始化加载工具列表 */
  loadToolOptions();

  /** 分类选项 */
  const classOptions = ref([]);

  /** 获取分类列表 */
  const loadClassOptions = async () => {
    try {
      const data = await queryClass();
      classOptions.value = data.map((item) => ({
        value: item.id,
        label: item.name
      }));
    } catch (e) {
      EleMessage.error('获取分类列表失败');
    }
  };

  /** 初始化加载分类列表 */
  loadClassOptions();

  /** 应用类型选项 */
  const typeOptions = ref([
    { value: 'CHAT', label: '聊天助手' }
    // { value: 'AGENT', label: '智能体' },
    // { value: 'WORK_FLOW', label: '工作流' }
  ]);

  /** 获取应用类型显示文本 */
  const getTypeLabel = (type) => {
    const option = typeOptions.value.find((item) => item.value === type);
    return option ? option.label : type;
  };

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'name',
      label: '应用名称',
      minWidth: 110
    },
    /*{
      prop: 'type',
      label: '应用类型',
      width: 100,
      formatter: (row) => {
        return getTypeLabel(row.type);
      }
    },*/
    {
      prop: 'classId',
      label: '应用分类',
      minWidth: 100,
      formatter: (row) => {
        return getLabelByValue(row.classId, classOptions.value);
      }
    },
    {
      prop: 'modelId',
      label: '关联模型',
      formatter: (row) => {
        return getLabelByValue(row.modelId, modelOptions.value);
      }
    },
    {
      prop: 'prologue',
      label: '开场白',
      minWidth: 150,
      showOverflowTooltip: true
    },
    {
      prop: 'cover',
      label: '应用封面'
    },
    {
      prop: 'knowledgeIds',
      label: '知识库',
      formatter: (row) => {
        if (!row.knowledgeIds?.length) return '-';
        return row.knowledgeIds
          .map((id) => getLabelByValue(id, knowledgeOptions.value))
          .filter((name) => name)
          .join(', ');
      }
    },
    {
      prop: 'maxResults',
      label: '最大结果数'
    },
    {
      prop: 'minScore',
      label: '最小相似度分数'
    },
    {
      prop: 'toolIds',
      label: '关联工具',
      minWidth: 120,
      formatter: (row) => {
        if (!row.toolIds?.length) return '-';
        return row.toolIds
          .map((id) => getLabelByValue(id, toolOptions.value))
          .filter((name) => name)
          .join(', ');
      }
    },
    {
      prop: 'prompt',
      label: '提示词'
    },
    {
      prop: 'status',
      label: '状态',
      align: 'center',
      width: 100,
      slot: 'status'
    },
    {
      columnKey: 'action',
      label: '操作',
      slot: 'action',
      width: 240,
      fixed: 'right'
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return queryPage({ ...where, ...orders, ...filters, page, limit });
  };

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 删除 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除"' + rows.map((d) => d.name).join(', ') + '"吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  // 聊天相关的响应式变量
  const chatVisible = ref(false);
  const currentApp = ref({});

  // 添加打开聊天窗口的方法
  const openChat = (row) => {
    currentApp.value = row;
    chatVisible.value = true;
  };

  const router = useRouter();

  /** 打开密钥配置页面 */
  const openApiKeys = (row) => {
    router.push({
      path: '/aigc/appapi',
      query: {
        appId: row.id,
        appName: row.name
      }
    });
  };
</script>

<script>
  export default {
    name: 'AigcApp'
  };
</script>

<style scoped>
  .el-link {
    font-weight: unset !important;
  }
</style>
