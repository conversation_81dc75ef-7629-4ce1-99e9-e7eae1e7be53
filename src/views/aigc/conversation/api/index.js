import request from '@/utils/request';

/**
 * 分页查询会话
 */
export async function queryPage(params) {
  const res = await request.get('/aigc/conversation/page', { params });
  if (res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.message));
}

/**
 * 批量删除会话
 */
export async function removes(ids) {
  const res = await request.post('/aigc/conversation/remove', ids);
  if (res.data != null) {
    return res.data;
  }
  return Promise.reject(new Error(res.message));
}
