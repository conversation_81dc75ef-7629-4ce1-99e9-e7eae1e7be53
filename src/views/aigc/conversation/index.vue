<template>
  <ele-page flex-table>
    <search @search="reload" />
    <ele-card
      flex-table
      :body-style="{ padding: '0 5px 10px 5px!important', overflow: 'hidden' }"
    >
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        cache-key="aigcConversation"
      >
        <template #toolbar>
          <el-button
            type="danger"
            size="small"
            class="ele-btn-icon"
            :icon="DeleteOutlined"
            @click="remove()"
          >
            删除
          </el-button>
        </template>
        <template #action="{ row }">
          <el-link type="primary" :underline="false" @click="viewDetail(row)">
            查看
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" :underline="false" @click="remove(row)">
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { ref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { DeleteOutlined } from '@/components/icons';
  import Search from './components/search.vue';
  import { queryPage, removes } from './api';
  import { useRouter } from 'vue-router';

  const router = useRouter();

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'username',
      label: '用户名'
    },
    {
      prop: 'title',
      label: '会话标题'
    },
    {
      prop: 'chatTotal',
      label: '对话次数',
      width: 100,
      align: 'center'
    },
    {
      prop: 'tokenUsed',
      label: 'Token消耗量',
      width: 100,
      align: 'center'
    },
    {
      prop: 'endTime',
      label: '最后一次对话时间',
      width: 160,
      align: 'center'
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 160,
      align: 'center'
    },
    {
      columnKey: 'action',
      label: '操作',
      slot: 'action',
      width: 140,
      fixed: 'right'
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders }) => {
    return queryPage({ ...where, ...orders, page, limit });
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 查看详情 */
  const viewDetail = (row) => {
    router.push({
      path: '/aigc/message',
      query: {
        conversationId: row.id,
        title: row.title // 可选：传递会话标题
      }
    });
  };

  /** 删除 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm('确定要删除选中的会话吗?', '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>

<style scoped>
  .el-link {
    font-weight: unset !important;
  }
</style>
