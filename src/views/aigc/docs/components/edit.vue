<template>
  <ele-drawer
    :size="800"
    :title="isUpdate ? '修改' : '新建'"
    :append-to-body="true"
    style="max-width: 100%"
    :destroy-on-close="true"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <pro-form
      ref="formRef"
      :model="form"
      :items="items"
      :grid="{ span: 24 }"
      :labelWidth="110"
      @updateValue="setFieldValue"
    />
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="onSubmit">
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, watch, h } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { EleMessage } from 'ele-admin-plus';
  import { operation } from '../api';
  import ProForm from '@/components/ProForm/index.vue';
  import ByteMdEditor from '@/components/ByteMdEditor/index.vue';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object,
    /** 知识库ID */
    knowledgeId: {
      type: String,
      required: true
    }
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    id: void 0,
    slicenum: void 0,
    url: '',
    content: '',
    knowledgeId: '',
    createtime: '',
    name: '',
    origin: '',
    fileSize: void 0,
    sliceStatus: void 0,
    type: ''
  });

  // 文档类型选项
  const typeOptions = [
    { label: '上传', value: 'UPLOAD' },
    { label: '录入', value: 'INPUT' }
  ];

  /** 表单项 */
  const items = ref([
    {
      prop: 'name',
      label: '文档名称',
      type: 'input',
      required: true,
      props: {
        placeholder: '请输入文档名称'
      }
    },
    {
      prop: 'content',
      label: '文档内容',
      type: 'editor',
      required: true,
      props: {
        height: '500px',
        config: {
          mode: 'split',
          theme: 'light'
        }
      }
    }
  ]);

  /** 提交状态 */
  const loading = ref(false);

  /** 提交 */
  const onSubmit = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      operation(form)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          assignFields({
            ...props.data
          });
          isUpdate.value = true;
        } else {
          resetFields();
          // 新建时设置默认值
          form.type = 'INPUT';
          form.knowledgeId = props.knowledgeId;
          formRef.value?.clearValidate?.();
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );

  // 添加样式
  const style = document.createElement('style');
  style.textContent = `
  .markdown-editor-form-item .el-form-item__content {
    height: 500px;
  }
  `;
  document.head.appendChild(style);
</script>
