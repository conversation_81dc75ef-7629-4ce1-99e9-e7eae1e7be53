<template>
  <ele-card :body-style="{ padding: '16px 0 0px 0px !important' }">
    <el-form
      size="small"
      label-width="72px"
      @keyup.enter="search"
      @submit.prevent=""
    >
      <el-row :gutter="8">
        <!-- 搜索条件 -->
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="文档名称">
            <el-select
              v-model="form.docsId"
              clearable
              placeholder="请选择文档"
              class="full-width"
              :disabled="!docsList.length"
            >
              <el-option
                v-for="item in docsList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="36" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { useRoute } from 'vue-router';
  import { query as queryDocs } from '../../docs/api';
  import { EleMessage } from 'ele-admin-plus';

  const emit = defineEmits(['search']);
  const route = useRoute();

  /** 文档列表 */
  const docsList = ref([]);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    id: '',
    createTime: '',
    docsId: route.query.docsId || '',
    wordNum: '',
    vectorId: '',
    knowledgeId: route.query.knowledgeId || '',
    content: '',
    name: '',
    status: ''
  });

  /** 加载文档列表 */
  const loadDocsList = async () => {
    try {
      const res = await queryDocs({ knowledgeId: route.query.knowledgeId });
      docsList.value = res || [];
    } catch (e) {
      console.error('加载文档列表失败:', e);
      EleMessage.error('加载文档列表失败');
    }
  };

  /** 搜索 */
  const search = () => {
    const searchParams = {
      ...form,
      knowledgeId: route.query.knowledgeId,
      docsId: form.docsId || route.query.docsId
    };
    emit('search', searchParams);
  };

  /** 重置 */
  const reset = () => {
    resetFields();
    form.knowledgeId = route.query.knowledgeId || '';
    form.docsId = route.query.docsId || '';
    search();
  };

  /** 初始化 */
  onMounted(() => {
    if (route.query.knowledgeId) {
      loadDocsList();
      search();
    }
  });
</script>

<style scoped>
  .full-width {
    width: 100%;
  }
</style>
