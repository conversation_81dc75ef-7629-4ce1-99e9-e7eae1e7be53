import request from '@/utils/request';

/**
 * 查询不分页知识库列表
 */
export async function query(params) {
  const res = await request.get('/aigc/AigcKnowledge', { params });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询知识库
 */
export async function queryPage(params) {
  const res = await request.get('/aigc/AigcKnowledge/page', { params });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询知识库
 */
export async function getById(id) {
  const res = await request.get('/aigc/AigcKnowledge/' + id);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改知识库
 */
export async function operation(data) {
  const res = await request.post('/aigc/AigcKnowledge/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除知识库
 */
export async function removes(data) {
  const res = await request.post('/aigc/AigcKnowledge/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/** 上传文件 */
export async function upload(data) {
  return request.post('/aigc/AigcKnowledge/upload', data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/** 手动录入 */
export async function input(data) {
  return request.post('/aigc/AigcKnowledge/input', data);
}

/**
 * 向量搜索
 */
export async function search(data) {
  const res = await request.post('/aigc/AigcKnowledge/search', data);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
