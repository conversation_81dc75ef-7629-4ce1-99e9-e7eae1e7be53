<template>
  <ele-drawer
    :size="530"
    :title="isUpdate ? '修改' : '新建'"
    :append-to-body="true"
    style="max-width: 100%"
    :destroy-on-close="true"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <pro-form
      ref="formRef"
      :model="form"
      :items="items"
      :grid="{ span: 24 }"
      :labelWidth="110"
      @updateValue="setFieldValue"
    />
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="onSubmit">
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, watch, onMounted } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { EleMessage } from 'ele-admin-plus';
  import { operation } from '../api';
  import { query as getModelList } from '../../model/api';
  import { queryPage as getEmbedStoreList } from '../../embedstore/api';
  import ProForm from '@/components/ProForm/index.vue';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 向量模型选项 */
  const modelOptions = ref([]);

  /** 向量数据库选项 */
  const embedStoreOptions = ref([]);

  /** 加载向量模型列表 */
  const loadModelOptions = async () => {
    try {
      const res = await getModelList({ type: 'EMBEDDING' });
      modelOptions.value = res.map((item) => ({
        label: item.name,
        value: item.id
      }));
    } catch (e) {
      console.error('加载向量模型失败:', e);
      EleMessage.error('加载向量模型失败');
    }
  };

  /** 加载向量数据库列表 */
  const loadEmbedStoreOptions = async () => {
    try {
      const res = await getEmbedStoreList({});
      embedStoreOptions.value = res.list.map((item) => ({
        label: item.name,
        value: item.id
      }));
    } catch (e) {
      console.error('加载向量数据库失败:', e);
      EleMessage.error('加载向量数据库失败');
    }
  };

  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    id: void 0,
    cover: '',
    embedModelId: '',
    des: '',
    name: '',
    createTime: '',
    embedStoreId: '',
    maxSegmentSize: 500,
    maxOverlapSize: 50
  });

  /** 表单项 */
  const items = ref([
    {
      prop: 'name',
      label: '名称',
      type: 'input',
      required: true,
      props: {
        placeholder: '请输入知识库名称'
      }
    },
    {
      prop: 'embedStoreId',
      label: '向量数据库',
      type: 'select',
      required: true,
      options: embedStoreOptions,
      props: {
        placeholder: '请选择向量数据库'
      }
    },
    {
      prop: 'embedModelId',
      label: '向量模型',
      type: 'select',
      required: true,
      options: modelOptions,
      props: {
        placeholder: '请选择向量模型'
      }
    },
    {
      prop: 'cover',
      label: '封面',
      type: 'input',
      props: {
        placeholder: '请输入封面'
      }
    },
    {
      prop: 'des',
      label: '描述',
      type: 'textarea',
      props: {
        placeholder: '请输入知识库描述',
        rows: 4
      }
    },
    {
      prop: 'maxSegmentSize',
      label: '最大的分段数',
      type: 'slider',
      required: true,
      props: {
        'show-tooltip': true,
        step: 1,
        min: 200,
        max: 4000
      }
    },
    {
      prop: 'maxOverlapSize',
      label: '最大重叠数',
      type: 'slider',
      required: true,
      props: {
        'show-tooltip': true,
        step: 1,
        min: 50,
        max: 1000
      }
    }
  ]);

  /** 提交状态 */
  const loading = ref(false);

  /** 提交 */
  const onSubmit = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      operation(form)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  // 修改组件挂载时的加载逻辑
  onMounted(async () => {
    await Promise.all([loadModelOptions(), loadEmbedStoreOptions()]);
  });

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          assignFields({
            ...props.data
          });
          isUpdate.value = true;
        } else {
          resetFields();
          formRef.value?.clearValidate?.();
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>
