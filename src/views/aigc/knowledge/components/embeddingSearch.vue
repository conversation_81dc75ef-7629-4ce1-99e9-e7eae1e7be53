<template>
  <el-drawer
    v-model="visible"
    :title="`${knowledgeName} - 向量搜索`"
    size="65%"
    :destroy-on-close="true"
    direction="rtl"
  >
    <div class="embedding-search">
      <!-- 搜索框 -->
      <div class="header">
        <div class="search-box">
          <el-input
            v-model="searchText"
            type="textarea"
            :rows="3"
            placeholder="请输入要搜索的内容"
            class="search-input"
          />
          <el-button type="primary" :loading="loading" @click="handleSearch">
            <el-icon><search /></el-icon>
            搜索
          </el-button>
        </div>
      </div>

      <!-- 搜索结果 -->
      <div class="content">
        <div v-if="searchResults.length > 0" class="search-results">
          <div class="result-header">
            <el-icon><info-filled /></el-icon>
            搜索结果 (共{{ searchResults.length }}条)
          </div>
          <el-scrollbar>
            <div
              v-for="(item, index) in searchResults"
              :key="index"
              class="result-item"
            >
              <div class="result-content">
                <div class="doc-info">
                  <el-tag size="small" type="info">{{ item.docsName }}</el-tag>
                  <span class="index">段落索引: {{ item.index }}</span>
                </div>
                <div class="content" :class="{ 'is-expanded': item.expanded }">
                  <div class="text">{{ item.text }}</div>
                  <div
                    v-if="item.text.length > 200"
                    class="expand-btn"
                    @click="toggleExpand(index)"
                  >
                    {{ item.expanded ? '收起' : '展开' }}
                    <el-icon
                      class="icon"
                      :class="{ 'is-expanded': item.expanded }"
                    >
                      <arrow-down />
                    </el-icon>
                  </div>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </div>

        <!-- 空状态 -->
        <el-empty v-else-if="searched" description="暂无搜索结果" />
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { ElMessage } from 'element-plus';
  import { Search, InfoFilled, ArrowDown } from '@element-plus/icons-vue';
  import { search } from '../api';

  const props = defineProps({
    knowledgeId: {
      type: [String, Number],
      required: true
    },
    modelValue: {
      type: Boolean,
      default: false
    },
    knowledgeName: {
      type: String,
      default: ''
    }
  });

  const emit = defineEmits(['update:modelValue', 'done']);

  /** 控制抽屉显示/隐藏 */
  const visible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
  });

  const loading = ref(false);
  const searchText = ref('');
  const searchResults = ref([]);
  const searched = ref(false);

  /** 执行搜索 */
  const handleSearch = async () => {
    if (!searchText.value.trim()) {
      ElMessage.warning('请输入搜索内容');
      return;
    }
    loading.value = true;
    try {
      const res = await search({
        knowledgeId: props.knowledgeId,
        content: searchText.value.trim()
      });
      searchResults.value = (res || []).map((item) => ({
        ...item,
        expanded: false
      }));
      searched.value = true;
    } catch (error) {
      ElMessage.error(error.message || '搜索失败');
    } finally {
      loading.value = false;
    }
  };

  /** 切换展开/收起状态 */
  const toggleExpand = (index) => {
    searchResults.value[index].expanded = !searchResults.value[index].expanded;
  };
</script>

<style scoped lang="scss">
  .embedding-search {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }

  .header {
    background: #fff;
    padding: 16px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    .search-box {
      display: flex;
      gap: 12px;
      align-items: flex-start;

      .search-input {
        flex: 1;
      }

      .el-button {
        margin-top: 4px;
        .el-icon {
          margin-right: 4px;
        }
      }
    }
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    min-height: 0;

    .search-results {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      :deep(.el-scrollbar) {
        flex: 1;
        height: 0;
      }

      .result-header {
        padding: 12px 16px;
        background: #f5f7fa;
        border-bottom: 1px solid #e4e7ed;
        font-weight: 500;
        color: #606266;
        display: flex;
        align-items: center;
        gap: 6px;

        .el-icon {
          color: var(--el-color-primary);
        }
      }

      .result-item {
        padding: 16px;
        border-bottom: 1px solid #e4e7ed;

        &:last-child {
          border-bottom: none;
        }

        .result-content {
          .doc-info {
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;

            .index {
              color: #909399;
              font-size: 13px;
            }
          }

          .content {
            color: #303133;
            line-height: 1.6;
            font-size: 14px;
            background: #f8f9fa;
            padding: 12px;
            border-radius: 4px;
            position: relative;

            .text {
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 3;
              -webkit-box-orient: vertical;
              white-space: pre-wrap;
              margin-bottom: 4px;
            }

            &.is-expanded {
              .text {
                -webkit-line-clamp: unset;
              }
            }

            .expand-btn {
              color: var(--el-color-primary);
              font-size: 13px;
              cursor: pointer;
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 4px;
              padding-top: 4px;
              border-top: 1px solid #e4e7ed;
              margin-top: 8px;

              .icon {
                transition: transform 0.3s;
                font-size: 12px;

                &.is-expanded {
                  transform: rotate(180deg);
                }
              }

              &:hover {
                opacity: 0.8;
              }
            }
          }
        }
      }
    }
  }

  :deep(.el-empty) {
    padding: 60px 0;
    margin: auto;
  }
</style>
