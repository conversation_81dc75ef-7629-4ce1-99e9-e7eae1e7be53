<template>
  <ele-drawer
    :size="530"
    :title="isUpdate ? '修改' : '新建'"
    :append-to-body="true"
    style="max-width: 100%"
    :destroy-on-close="true"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <pro-form
      ref="formRef"
      :model="form"
      :items="items"
      :grid="{ span: 24 }"
      :labelWidth="110"
      @updateValue="setFieldValue"
    />
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="onSubmit">
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, unref, watch, computed, nextTick } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { EleMessage } from 'ele-admin-plus';
  import { operation } from '../api';
  import ProForm from '@/components/ProForm/index.vue';
  import { useRouter } from 'vue-router';
  import { typeOptions, typeProviderOptions } from '../config';

  const BASE_URL = import.meta.env.BASE_URL;
  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  const { currentRoute } = useRouter();
  const { params, path } = unref(currentRoute);
  const userType = path.split('/')[4];
  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 表单实例 */
  const formRef = ref(null);
  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    id: void 0,
    type: '',
    provider: '',
    model: '',
    name: '',
    responseLimit: 2000, // 设置默认值
    temperature: 0.2, // 设置默认值
    topP: 0.8, // 设置默认值
    baseUrl: '',
    apiKey: '',
    status: ''
  });

  // 根据选择的类型获取供应商选项
  const filteredProviderOptions = computed(() => {
    return typeProviderOptions[form.type] || [];
  });

  /** 表单项 */
  const items = ref([
    {
      prop: 'type',
      label: '模型类型',
      type: 'select',
      required: true,
      options: typeOptions
    },
    {
      prop: 'provider',
      label: '供应商',
      type: 'select',
      required: true,
      options: filteredProviderOptions
    },
    {
      prop: 'model',
      label: '模型名称',
      type: 'input',
      required: true
    },
    {
      prop: 'name',
      label: '模型别名',
      type: 'input'
    },
    {
      prop: 'responseLimit',
      label: '回复上限',
      type: 'slider',
      required: true,
      props: {
        'show-tooltip': true,
        step: 1,
        min: 1,
        max: 8192
      }
    },
    {
      prop: 'temperature',
      label: '生成随机性',
      type: 'slider',
      required: true,
      props: {
        'show-tooltip': true,
        step: 0.05,
        min: 0,
        max: 2,
        'format-tooltip': (val) => val.toFixed(2)
      }
    },
    {
      prop: 'topP',
      label: 'Top P',
      type: 'slider',
      required: true,
      props: {
        'show-tooltip': true,
        step: 0.1,
        min: 0,
        max: 1,
        'format-tooltip': (val) => val.toFixed(1)
      }
    },
    {
      prop: 'baseUrl',
      label: 'Base Url',
      type: 'input'
    },
    {
      prop: 'apiKey',
      label: 'Api Key',
      type: 'input'
    },
    {
      prop: 'status',
      label: '状态',
      type: 'dictSelect',
      props: {
        code: 'zt',
        dicQueryParams: {
          getValType: 'name'
        }
      },
      required: true
    }
  ]);

  /** 提交状态 */
  const loading = ref(false);
  /** 提交 */
  const onSubmit = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      // let data = toFormData({...form})
      loading.value = true;
      operation(form)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          // 确保在赋值前已经有正确的供应商选项
          form.type = props.data.type; // 先设置类型，这样供应商选项会更新
          nextTick(() => {
            // 使用 nextTick 确保供应商选项已更新
            assignFields({
              ...props.data
            });
          });
          isUpdate.value = true;
        } else {
          resetFields();
          formRef.value?.clearValidate?.();
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );

  // 修改监听类型变化的逻辑
  watch(
    () => form.type,
    (newType, oldType) => {
      // 如果是编辑状态下的初始赋值，不清空供应商和模型
      if (isUpdate.value && !oldType) {
        return;
      }
      // 当类型改变时，清空供应商和模型
      form.provider = '';
      form.model = '';
    }
  );

  // 修改监听供应商变化的逻辑
  watch(
    () => form.provider,
    (newProvider, oldProvider) => {
      // 如果是编辑状态下的初始赋值，不清空模型
      if (isUpdate.value && !oldProvider) {
        return;
      }
      // 当供应商改变时，清空模型
      form.model = '';
    }
  );
</script>

<style scoped>
  .el-slider {
    width: 100%;
    margin-top: 8px;
  }
</style>
