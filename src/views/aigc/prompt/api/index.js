import request from '@/utils/request';

/**
 * 保存提示词
 * @param {Object} data - 提示词参数
 * @returns {Promise}
 */
export async function save(data) {
  const res = await request.post('/aigc/prompt/save', data);
  if (res.data != null) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.message));
}

/**
 * 更新提示词
 * @param {Object} data - 提示词参数
 * @returns {Promise}
 */
export async function update(data) {
  const res = await request.post('/aigc/prompt/update', data);
  if (res.data != null) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.message));
}

/**
 * 删除提示词
 * @param {Number} id - 提示词ID
 * @returns {Promise}
 */
export async function remove(id) {
  const res = await request.post(`/aigc/prompt/remove/${id}`);
  if (res.data != null) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.message));
}

/**
 * 获取提示词详情
 * @param {Number} id - 提示词ID
 * @returns {Promise}
 */
export async function getDetail(id) {
  const res = await request.get(`/aigc/prompt/detail/${id}`);
  if (res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.message));
}

/**
 * 分页查询提示词
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export async function queryPage(params) {
  const res = await request.get('/aigc/prompt/page', { params });
  if (res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.message));
}

/**
 * 增加使用次数
 * @param {Number} id - 提示词ID
 * @returns {Promise}
 */
export async function increaseUseCount(id) {
  const res = await request.post(`/aigc/prompt/increase-use-count/${id}`);
  if (res.data != null) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.message));
}

/**
 * 批量删除提示词
 */
export async function removes(ids) {
  if (Array.isArray(ids)) {
    // 批量删除
    return Promise.all(ids.map((id) => remove(id)));
  } else {
    // 单个删除
    return remove(ids);
  }
}
