<template>
  <ele-table-select
    multiple
    clearable
    placeholder="请选择签到位置"
    value-key="id"
    label-key="name"
    v-model="selectedValue"
    :table-props="tableProps"
    :cache-data="cacheData"
    :popper-width="580"
  >
    <!--        @select="onSelectClick"-->
  </ele-table-select>
</template>

<script setup>
  import { reactive, ref, watch } from 'vue';
  import { getCheckinAddress } from '../../address/api/index.js';

  const emit = defineEmits(['updateValue', 'doneSelected']);

  const props = defineProps({
    isAddAddress: Boolean,
    cacheDataQdfw: Array
  });
  /** 表格下拉选中值 */
  const selectedValue = ref([]);

  /** 表格配置 */
  const tableProps = reactive({
    datasource: [],
    columns: [
      {
        type: 'selection',
        columnKey: 'selection',
        width: 50,
        align: 'center',
        fixed: 'left',
        reserveSelection: true
      },
      {
        type: 'index',
        columnKey: 'index',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'name',
        label: '签到位置',
        width: 190
      },
      {
        prop: 'type',
        label: '类型',
        sortable: 'custom',
        formatter: (row) => {
          let finallyVal = '';
          switch (row.type) {
            case 'poi':
              finallyVal = '圆形';
              break;
            case 'Polygon':
              finallyVal = '多边形';
              break;
            case 'Rectangle':
              finallyVal = '矩形';
              break;
            default:
              finallyVal = '圆形';
              break;
          }
          return finallyVal;
        }
      },
      {
        prop: 'fwbj',
        label: '签到范围半径(米)',
        sortable: 'custom'
      }
      // {
      //   columnKey: 'roles',
      //   label: '角色',
      //   slot: 'roles'
      // }
    ],
    showOverflowTooltip: true,
    toolbar: false,
    pagination: {
      pageSize: 6,
      layout: 'total, prev, pager, next, jumper',
      style: { padding: '0px' }
    },
    rowClickChecked: true
  });

  // /** 表格行点击事件 */
  // const onSelectClick = (row) => {
  //     // emit('doneSelected', row);
  // };

  /** 查询表格数据 */
  getCheckinAddress().then((data) => {
    tableProps.datasource = data;
  });

  // 回显数据
  const cacheData = ref();
  /** 更新选中数据 */
  watch(
    () => props.cacheDataQdfw,
    (val) => {
      if (val) {
        cacheData.value = val;
        let ids = val.map((item) => item.id);
        selectedValue.value = ids;
      }
    },
    {
      immediate: true
    }
  );

  /** 更新选中数据 */
  watch(
    () => selectedValue.value,
    (val) => {
      if (val) {
        emit('update:modelValue', val);
      }
    },
    {
      immediate: true
    }
  );

  watch(
    () => props.isAddAddress,
    (val) => {
      if (val) {
        /** 查询表格数据 */
        getCheckinAddress().then((data) => {
          tableProps.datasource = data;
        });
      }
    },
    {
      immediate: true
    }
  );
</script>
