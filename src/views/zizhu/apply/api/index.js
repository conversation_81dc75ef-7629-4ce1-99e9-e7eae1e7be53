/**
 * 表单申请
 * */
import request from '@/utils/request.js';

/**
 * 分页查询表单项目记录（权限标识：form:apply:list）
 */
export async function queryPageProjectInfo(params) {
  const res = await request.get('/form/apply/pageProjectInfo', { params });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询表单申请记录（权限标识：form:apply:list）
 */
export async function queryPageApplicationInfo(params) {
  const res = await request.get('/form/apply/pageApplicationInfo', { params });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询表单项目信息（权限标识：form:apply:list）
 */
export async function getFormApplyProjectInfoById(id) {
  const res = await request.get('/form/apply/projectInfo/' + id);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询表单申请记录（权限标识：form:apply:list）
 */
export async function getFormApplyApplicationInfoById(id, params) {
  const res = await request.get('/form/apply/applicationInfo/' + id, {
    params
  });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据项目id查询表单组
 */
export async function getFormApplyFieldGroupList(id) {
  const res = await request.get('/form/apply/' + id + '/fieldGroupList');
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据项目ID查询字典列表信息组
 */
export async function getDicListGroup(id) {
  const res = await request.get('/form/apply/' + id + '/dicListGroup');
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据项目ID查询字典列表信息字段
 */
export async function getDicListGroupField(id, dicGroupId) {
  const res = await request.get(
    '/form/apply/' + id + '/dicListGroupField/' + dicGroupId
  );
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据项目id查询表单字段
 */
export async function getFormApplyFieldList(id, params) {
  const res = await request.get('/form/apply/' + id + '/fieldList', { params });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加或修改表单申请记录（权限标识：form:apply:operation）
 * form
 */
export async function operationApply(data) {
  const res = await request.post('/form/apply/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加或修改表单申请记录（权限标识：form:apply:operation）
 * table
 */
export async function operationApplyListInfo(data) {
  const res = await request.post('/form/apply/listInfoOperation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除表单申请记录（权限标识：form:apply:remove）
 */
export async function removesApply(data) {
  const res = await request.post('/form/apply/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除表单申请记录（权限标识：form:apply:remove）
 */
export async function removesApplyListInfo(data) {
  const res = await request.post('/form/apply/listInfoRemove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
