<template>
  <ele-page hide-footer flex-table>
    <ele-card :body-style="{ padding: '0px !important' }">
      <template #header>
        <ele-tabs
          type="plain"
          size="small"
          v-model="active"
          :items="tabsItems"
          @tabClick="onFormTabChange"
        >
          <template #label="{ item, label }">
            <span>
              <el-avatar
                :size="20"
                shape="square"
                :style="'background-color:' + (!item.disabled ? '#1677ff' : '')"
              >
                {{ item.sort }}
              </el-avatar>
              {{ label }}</span
            >
          </template>
        </ele-tabs>
      </template>
      <template #extra>
        <el-button size="small" type="primary" @click="showYL = true">
          申请表单预览
        </el-button>
        <el-button size="small" type="primary" @click="showYL = true">
          发布
        </el-button>
      </template>
      <div
        ref="containerRef"
        :style="{ height: pageHeight + 'px', overflow: 'auto' }"
      >
        <div
          v-if="active === 'info' && RandomString"
          style="margin-top: 2px !important"
        >
          <SetPerSelector
            paramMode="zzbdweh"
            hearder="项目基础设置"
            :perSelectedData="perSelectedData"
            :RandomString="RandomString"
            :currentXmId="currentXmId"
            :userType="userType"
            :pageHeight="pageHeight - 10"
            @done="onDoneSelector"
          />
        </div>
        <div v-if="active === 'field'" style="margin-top: 2px !important">
          <DetailsFormfield
            :RandomString="
              newRandomStringfield ? newRandomStringfield : RandomStringfield
            "
            :currentXmId="currentXmId"
            :approverData="formData"
            @onDoneGroup="handleDoneGroup"
          />
        </div>
        <div v-if="active === 'workflow'" style="margin-top: 2px !important">
          <ele-alert
            v-if="!projectWorkFlow"
            :closable="false"
            style="margin: 5px; padding: 8px !important"
          >
            <span style="font-size: 12px"
              >项目审核流程设置系统提供两种方式，方式一您可以选择使用系统默认流程；方式二您如需要自定义流程，请点击创建流程模板按钮，系统会自动生成流程模板，您可以根据流程模板进行修改，修改完成后点击保存按钮，即可完成流程模板的创建。</span
            >
          </ele-alert>
          <el-form
            v-if="!projectWorkFlow"
            size="small"
            @submit.prevent=""
            style="margin: 12px 5px 0px 5px !important"
          >
            <el-row>
              <el-col :lg="6" :md="12" :sm="12" :xs="24">
                <el-form-item label="方式一：请选择现有审核流程">
                  <el-select
                    v-model="workFlowSelected"
                    class="ele-fluid"
                    @change="workFlowSelectedChange"
                  >
                    <el-option
                      v-for="wflow in workFlowData"
                      :key="wflow.id"
                      :value="wflow.id"
                      :label="wflow.name"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :lg="1" :md="12" :sm="12" :xs="24" />
              <el-col :lg="6" :md="12" :sm="12" :xs="24">
                <el-form-item label="方式二：">
                  <el-button type="primary" @click="createWorkFlow"
                    >新建流程模板</el-button
                  >
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>

          <Workflow
            v-if="workFlow"
            :pageHeight="projectWorkFlow ? pageHeight - 20 : pageHeight - 132"
            :workflowId="
              workFlowSelected ? workFlowSelected : workFlowSelectedInit
            "
            @update:nodeConfig="handleDoneNodeConfig"
          />
        </div>
      </div>
    </ele-card>

    <!-- 底部工具栏 -->
    <ele-bottom-bar>
      <!--      <ele-text v-if="validMsg" type="danger" :icon="CloseCircleOutlined">-->
      <!--        <span>{{ validMsg }}</span>-->
      <!--      </ele-text>-->
      <template #extra>
        <el-button size="small" @click="onBack">关闭</el-button>
        <el-button
          size="small"
          type="primary"
          :loading="loading"
          @click="onSubmit"
        >
          保存
        </el-button>
        <!--        <el-tooltip content="1,2,3 步骤填写完整后，才能提交！" effect="customized">-->
        <!--        </el-tooltip>-->
      </template>
    </ele-bottom-bar>

    <DETAILS_YL
      v-model="showYL"
      :currentXmId="currentXmId"
      :routeType="routeType"
    />
  </ele-page>
</template>

<script setup>
  import { computed, onMounted, ref, unref } from 'vue';
  import { useUserStore } from '@/store/modules/user';
  import DetailsFormfield from '@/views/zizhu/details-form/components/field.vue';
  import Workflow from '@/views/dingding-flow/components/workflow.vue';
  import { EleMessage } from 'ele-admin-plus';
  import { getWorkFlow } from '@/views/dingding-flow/api/index.js';
  import { useRouter } from 'vue-router';
  import { usePageTab } from '@/utils/use-page-tab.js';
  import SetPerSelector from '@/components/PerSelector/components/set-per-selector.vue';
  import { generateRandomString, groupArr } from '@/utils/common.js';
  import { operation as formTemplatefieldOperation } from '@/views/zizhu/api/form-template-field-index.js';
  import { editBaseInfo } from '@/views/zizhu/api/form-group-index.js';
  import { mapMutations } from '@/plugins/lib.js';
  import {
    getProjectWorkFlow,
    operationWorkflow
  } from '@/views/zizhu/api/index.js';
  import DETAILS_YL from '@/views/zizhu/details-form/details_yl.vue';

  let { setSharedZizhuData } = mapMutations();
  const userStore = useUserStore();

  const { removePageTab, getRouteTabKey, addPageTab } = usePageTab();
  const { setTableId, setIsTried } = mapMutations();

  const { currentRoute, push } = useRouter();
  const { query, path } = unref(currentRoute);
  let params = JSON.parse(decodeURIComponent(atob(query.zz)));
  let userType = params?.userType;
  let routeType = params?.routeType;
  let currentXmId = params?.xmId;
  let currentXmmc = params?.xmmc;
  let enterType = params?.enterType;
  routeType = routeType ? routeType : path.split('/')[3];

  /** 提交状态 */
  const loading = ref(false);

  /** 标签页选中 */
  const active = ref('info');

  const workFlow = ref(false);
  const workFlowData = ref([]);
  const workFlowSelected = ref(null);
  const workFlowSelectedInit = ref(null);
  const newRandomStringfield = ref(null);

  const tipList = ref([]);
  const tipVisible = ref(false);
  /** 已绑定的工作流 */
  const projectWorkFlow = ref(null);

  const perSelectedData = ref([]);
  const onDoneSelector = (data) => {
    perSelectedData.value = data;
  };

  /** 新建流程模板 */
  const createWorkFlow = () => {
    workFlow.value = true;
    workFlowSelectedInit.value = 'workFlowSelected';
    workFlowSelected.value = null;
  };

  /** 选择编辑流程模版 */
  const workFlowSelectedChange = (event) => {
    if (event) {
      workFlowSelectedInit.value = null;
      workFlow.value = true;
    }
  };
  /** 表单资助申请字段 */
  const formData = ref(null);
  const handleDoneGroup = (data) => {
    formData.value = data ?? [];
  };

  /** 是否显示编辑弹窗 */
  const showYL = ref(false);

  /** 流程配置 */
  const processConfig = ref({});
  const handleDoneNodeConfig = (data) => {
    processConfig.value = data;
  };

  const getWorkFlowById = () => {
    getWorkFlow()
      .then((list) => {
        if (list) {
          workFlowData.value = list;
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  const queryProjectWorkFlow = () => {
    getProjectWorkFlow(currentXmId)
      .then((list) => {
        if (list) {
          projectWorkFlow.value = list;
          workFlowSelected.value = list.id;
          workFlowSelectedChange(true);
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  const onSubmit = async () => {
    if (active.value === 'info') {
      let setInfoData = groupArr(
        perSelectedData.value,
        'configTitle',
        'configKey'
      );
      console.log(JSON.stringify(setInfoData));
      let newFromArray = [];
      setInfoData.forEach((item) => {
        if (item.fieldId === 'zzdtxx') {
          //多条信息，直接循环创建组信息
          item.list.forEach((ilist) => {
            let form = {
              // id: ilist?.groupId,
              infoType: 'list',
              groupName: ilist.name,
              dicGroupId: ilist.value,
              // groupList: [],
              // formTemplateFields: [],
              listFlag: '是',
              type: routeType,
              // year: '',
              projectId: currentXmId
              // sort: void 0,
            };
            newFromArray.push(form);
            // save(form)
          });
        } else if (item.fieldId === 'zzjbxx') {
          //基本信息，重新组装数据（formTemplateFields）
          item.list.forEach((ilist) => {
            ilist.projectId = currentXmId;
            ilist.infoType = 'base';
          });
          let form = {
            // id: item.list[0].groupId,
            infoType: 'base',
            groupName: item.type,
            dicGroupId: '',
            // groupList: [],
            formTemplateFields: item.list,
            listFlag: '否',
            type: routeType,
            // year: '',
            projectId: currentXmId
            // sort: void 0,
          };
          newFromArray.push(form);
        }
      });
      save(newFromArray);
    } else if (active.value === 'field') {
      formData.value.forEach((row) => {
        for (let i in row) {
          if (typeof row[i] === 'boolean') {
            row[i] = row[i] === true ? '是' : '否';
          }
        }
      });
      loading.value = true;
      formTemplatefieldOperation(formData.value)
        .then((msg) => {
          newRandomStringfield.value = generateRandomString(18);
          loading.value = false;
          EleMessage.success(msg);
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    } else if (active.value === 'workflow') {
      saveWorkFlow();
    }
  };

  const reErr = ({ childNode }) => {
    if (childNode) {
      let { type, error, nodeName, conditionNodes } = childNode;
      if (type == 1 || type == 2) {
        if (error) {
          tipList.value.push({
            name: nodeName,
            type: ['', '审核人', '抄送人'][type]
          });
        }
        reErr(childNode);
      } else if (type == 3) {
        reErr(childNode);
      } else if (type == 4) {
        reErr(childNode);
        for (var i = 0; i < conditionNodes.length; i++) {
          if (conditionNodes[i].error) {
            tipList.value.push({
              name: conditionNodes[i].nodeName,
              type: '条件'
            });
          }
          reErr(conditionNodes[i]);
        }
      }
    } else {
      childNode = null;
    }
  };
  const saveWorkFlow = async () => {
    setIsTried(true);
    tipList.value = [];
    reErr(processConfig.value.nodeConfig);
    if (tipList.value.length != 0) {
      tipVisible.value = true;
      return;
    }

    let FlowData = processConfig.value;
    let workflowData = {
      id: workFlowSelected.value
        ? workFlowSelected.value
        : workFlowSelectedInit.value,
      name: FlowData.nodeConfig.nodeName, //流程名称
      projectId: currentXmId, //项目ID
      year: '', //年份
      workflowNodes: FlowData.nodeConfig.childNode
    };
    console.log('workflowData=====', workflowData);
    console.log('workflowData===JSON==', JSON.stringify(workflowData));
    loading.value = true;
    operationWorkflow(currentXmId, workflowData)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const save = (form) => {
    console.log('save=======', form);
    loading.value = true;
    editBaseInfo(currentXmId, form)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  const tabsItems = computed(() => {
    return [
      {
        label: '项目基础设置',
        name: 'info',
        disabled: false,
        sort: 1
      },
      {
        label: '申请字段设置',
        name: 'field',
        sort: 2
      },
      {
        label: '审核流程设置',
        name: 'workflow',
        sort: 3
      }
    ];
  });

  const RandomString = computed(() => {
    return active.value === 'info' ? generateRandomString(10) : '';
  });

  const RandomStringfield = computed(() => {
    return active.value === 'field' ? generateRandomString(18) : '';
  });

  /** tab选择改变事件 */
  const onFormTabChange = (event) => {
    if (event.props.name === 'workflow') {
      getWorkFlowById();
    }
  };

  /** 返回 */
  const onBack = () => {
    removePageTab({ key: getRouteTabKey() });
    let json = {};
    if (currentXmId) json.xmId = currentXmId;
    let path = enterType === 'list' ? '/zizhu/info/' : '/zizhu/details/';
    setSharedZizhuData(json);
    push({
      path: path + routeType
    });
  };

  const pageHeight = ref(0);
  onMounted(() => {
    // 获取页面高度
    pageHeight.value =
      document.querySelector('.ele-admin-content').clientHeight - 105;
    queryProjectWorkFlow();

    addPageTab({
      title: `${currentXmmc}-表单维护`,
      key: `/zizhu/details-form/${routeType}`,
      closable: true,
      meta: { icon: 'LinkOutlined' }
    });
  });
</script>

<script>
  export default {
    name: 'ZIZHUDETAILSFORM'
  };
</script>

<style lang="scss" scoped>
  .user-wrapper {
    display: flex;

    .user-side {
      width: 320px;
      margin: 0 16px 0 0;
      flex-shrink: 0;
    }

    .user-body {
      flex: 1;
    }
  }

  @media screen and (max-width: 928px) {
    .user-wrapper .user-side {
      width: 240px;
    }
  }

  @media screen and (max-width: 768px) {
    .user-wrapper {
      display: block;

      .user-side {
        width: auto;
        margin: 0 0 16px 0;
      }
    }
  }
</style>
