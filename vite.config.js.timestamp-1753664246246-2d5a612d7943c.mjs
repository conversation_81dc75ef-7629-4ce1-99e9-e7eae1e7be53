// vite.config.js
import { defineConfig } from "file:///Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform-ui/node_modules/vite/dist/node/index.js";
import vue from "file:///Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform-ui/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import { resolve } from "path";
import Compression from "file:///Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform-ui/node_modules/vite-plugin-compression/dist/index.mjs";
import Components from "file:///Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform-ui/node_modules/unplugin-vue-components/dist/vite.js";
import { ElementPlusResolver } from "file:///Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform-ui/node_modules/unplugin-vue-components/dist/resolvers.js";
import { EleAdminResolver } from "file:///Users/<USER>/WorkSpace/sanyth-dev/sanyth-dev-platform-ui/node_modules/ele-admin-plus/es/utils/resolvers.js";
var vite_config_default = defineConfig(({ command }) => {
  const isBuild = command === "build";
  const alias = {
    "@/": resolve("src") + "/",
    "vue-i18n": "vue-i18n/dist/vue-i18n.cjs.js"
  };
  const plugins = [vue()];
  if (isBuild) {
    plugins.push(
      Components({
        dts: false,
        resolvers: [
          ElementPlusResolver({
            importStyle: "sass"
          }),
          EleAdminResolver({
            importStyle: "sass"
          })
        ]
      })
    );
    plugins.push(
      Compression({
        disable: !isBuild,
        threshold: 10240,
        algorithm: "gzip",
        ext: ".gz"
      })
    );
  } else {
    alias["./as-needed"] = "./global-import";
  }
  return {
    server: {
      host: "0.0.0.0",
      // port: 8088,  // 这里写要改的端口
      proxy: {
        // 这里可以写你自己的后端接口地址，如：
        "/api": "http://localhost:9096"
        // '/api': 'http://************:9096'
        // 注意, 这种方式需要接口都有一个统一的前缀, 这里 /api 就是所有接口都会有的前缀
        // 如果接口没有统一前缀可以这样写最后把 /api 去掉
        // '/api': {
        //     target: 'http://localhost:9018',
        //     changeOrigin: true,
        //     rewrite: (path) => path.replace(/^\/api/, '')
        // }
      }
    },
    resolve: { alias },
    plugins,
    css: {
      preprocessorOptions: {
        scss: {
          api: "modern-compiler",
          additionalData: `@use "@/styles/variables.scss" as *;`,
          silenceDeprecations: ["legacy-js-api"]
        }
      }
    },
    optimizeDeps: {
      include: [
        "echarts/core",
        "echarts/charts",
        "echarts/renderers",
        "echarts/components",
        "vue-echarts",
        "echarts-wordcloud",
        "vuedraggable",
        "sortablejs",
        "xlsx"
      ]
    },
    build: {
      target: "es2015",
      chunkSizeWarningLimit: 2e3,
      rollupOptions: {
        output: {
          manualChunks: void 0
        },
        onwarn(warning, warn) {
          if (warning.code === "CIRCULAR_DEPENDENCY")
            return;
          if (warning.code === "THIS_IS_UNDEFINED")
            return;
          warn(warning);
        },
        treeshake: false
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcuanMiXSwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCIvVXNlcnMvcGF5bmV4Yy9Xb3JrU3BhY2Uvc2FueXRoLWRldi9zYW55dGgtZGV2LXBsYXRmb3JtLXVpXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ZpbGVuYW1lID0gXCIvVXNlcnMvcGF5bmV4Yy9Xb3JrU3BhY2Uvc2FueXRoLWRldi9zYW55dGgtZGV2LXBsYXRmb3JtLXVpL3ZpdGUuY29uZmlnLmpzXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ltcG9ydF9tZXRhX3VybCA9IFwiZmlsZTovLy9Vc2Vycy9wYXluZXhjL1dvcmtTcGFjZS9zYW55dGgtZGV2L3Nhbnl0aC1kZXYtcGxhdGZvcm0tdWkvdml0ZS5jb25maWcuanNcIjtpbXBvcnQgeyBkZWZpbmVDb25maWcgfSBmcm9tICd2aXRlJztcbmltcG9ydCB2dWUgZnJvbSAnQHZpdGVqcy9wbHVnaW4tdnVlJztcbmltcG9ydCB7IHJlc29sdmUgfSBmcm9tICdwYXRoJztcbmltcG9ydCBDb21wcmVzc2lvbiBmcm9tICd2aXRlLXBsdWdpbi1jb21wcmVzc2lvbic7XG5pbXBvcnQgQ29tcG9uZW50cyBmcm9tICd1bnBsdWdpbi12dWUtY29tcG9uZW50cy92aXRlJztcbmltcG9ydCB7IEVsZW1lbnRQbHVzUmVzb2x2ZXIgfSBmcm9tICd1bnBsdWdpbi12dWUtY29tcG9uZW50cy9yZXNvbHZlcnMnO1xuaW1wb3J0IHsgRWxlQWRtaW5SZXNvbHZlciB9IGZyb20gJ2VsZS1hZG1pbi1wbHVzL2VzL3V0aWxzL3Jlc29sdmVycyc7XG4vLyB2aXRlLmNvbmZpZy5qc1xuLy8gaW1wb3J0IGxhbmdKc3ggZnJvbSAndml0ZS1wbHVnaW4tbGFuZy1qc3gnXG5cbmV4cG9ydCBkZWZhdWx0IGRlZmluZUNvbmZpZygoeyBjb21tYW5kIH0pID0+IHtcbiAgY29uc3QgaXNCdWlsZCA9IGNvbW1hbmQgPT09ICdidWlsZCc7XG4gIGNvbnN0IGFsaWFzID0ge1xuICAgICdALyc6IHJlc29sdmUoJ3NyYycpICsgJy8nLFxuICAgICd2dWUtaTE4bic6ICd2dWUtaTE4bi9kaXN0L3Z1ZS1pMThuLmNqcy5qcydcbiAgfTtcbiAgLy8gbGFuZ0pzeFx1NUZDNVx1OTg3Qlx1NTcyOCB2dWUgXHU2M0QyXHU0RUY2XHU1MjREXG4gIGNvbnN0IHBsdWdpbnMgPSBbdnVlKCldO1xuICBpZiAoaXNCdWlsZCkge1xuICAgIC8vIFx1N0VDNFx1NEVGNlx1NjMwOVx1OTcwMFx1NUYxNVx1NTE2NVxuICAgIHBsdWdpbnMucHVzaChcbiAgICAgIENvbXBvbmVudHMoe1xuICAgICAgICBkdHM6IGZhbHNlLFxuICAgICAgICByZXNvbHZlcnM6IFtcbiAgICAgICAgICBFbGVtZW50UGx1c1Jlc29sdmVyKHtcbiAgICAgICAgICAgIGltcG9ydFN0eWxlOiAnc2FzcydcbiAgICAgICAgICB9KSxcbiAgICAgICAgICBFbGVBZG1pblJlc29sdmVyKHtcbiAgICAgICAgICAgIGltcG9ydFN0eWxlOiAnc2FzcydcbiAgICAgICAgICB9KVxuICAgICAgICBdXG4gICAgICB9KVxuICAgICk7XG4gICAgLy8gZ3ppcFx1NTM4Qlx1N0YyOVxuICAgIHBsdWdpbnMucHVzaChcbiAgICAgIENvbXByZXNzaW9uKHtcbiAgICAgICAgZGlzYWJsZTogIWlzQnVpbGQsXG4gICAgICAgIHRocmVzaG9sZDogMTAyNDAsXG4gICAgICAgIGFsZ29yaXRobTogJ2d6aXAnLFxuICAgICAgICBleHQ6ICcuZ3onXG4gICAgICB9KVxuICAgICk7XG4gIH0gZWxzZSB7XG4gICAgLy8gXHU1RjAwXHU1M0QxXHU3M0FGXHU1ODgzXHU1MTY4XHU1QzQwXHU1Qjg5XHU4OEM1XG4gICAgYWxpYXNbJy4vYXMtbmVlZGVkJ10gPSAnLi9nbG9iYWwtaW1wb3J0JztcbiAgfVxuICByZXR1cm4ge1xuICAgIHNlcnZlcjoge1xuICAgICAgaG9zdDogJzAuMC4wLjAnLFxuICAgICAgLy8gcG9ydDogODA4OCwgIC8vIFx1OEZEOVx1OTFDQ1x1NTE5OVx1ODk4MVx1NjUzOVx1NzY4NFx1N0FFRlx1NTNFM1xuICAgICAgcHJveHk6IHtcbiAgICAgICAgLy8gXHU4RkQ5XHU5MUNDXHU1M0VGXHU0RUU1XHU1MTk5XHU0RjYwXHU4MUVBXHU1REYxXHU3Njg0XHU1NDBFXHU3QUVGXHU2M0E1XHU1M0UzXHU1NzMwXHU1NzQwXHVGRjBDXHU1OTgyXHVGRjFBXG4gICAgICAgICcvYXBpJzogJ2h0dHA6Ly9sb2NhbGhvc3Q6OTA5NidcbiAgICAgICAgLy8gJy9hcGknOiAnaHR0cDovLzE5Mi4xNjguMS4xMzo5MDk2J1xuICAgICAgICAvLyBcdTZDRThcdTYxMEYsIFx1OEZEOVx1NzlDRFx1NjVCOVx1NUYwRlx1OTcwMFx1ODk4MVx1NjNBNVx1NTNFM1x1OTBGRFx1NjcwOVx1NEUwMFx1NEUyQVx1N0VERlx1NEUwMFx1NzY4NFx1NTI0RFx1N0YwMCwgXHU4RkQ5XHU5MUNDIC9hcGkgXHU1QzMxXHU2NjJGXHU2MjQwXHU2NzA5XHU2M0E1XHU1M0UzXHU5MEZEXHU0RjFBXHU2NzA5XHU3Njg0XHU1MjREXHU3RjAwXG4gICAgICAgIC8vIFx1NTk4Mlx1Njc5Q1x1NjNBNVx1NTNFM1x1NkNBMVx1NjcwOVx1N0VERlx1NEUwMFx1NTI0RFx1N0YwMFx1NTNFRlx1NEVFNVx1OEZEOVx1NjgzN1x1NTE5OVx1NjcwMFx1NTQwRVx1NjI4QSAvYXBpIFx1NTNCQlx1NjM4OVxuICAgICAgICAvLyAnL2FwaSc6IHtcbiAgICAgICAgLy8gICAgIHRhcmdldDogJ2h0dHA6Ly9sb2NhbGhvc3Q6OTAxOCcsXG4gICAgICAgIC8vICAgICBjaGFuZ2VPcmlnaW46IHRydWUsXG4gICAgICAgIC8vICAgICByZXdyaXRlOiAocGF0aCkgPT4gcGF0aC5yZXBsYWNlKC9eXFwvYXBpLywgJycpXG4gICAgICAgIC8vIH1cbiAgICAgIH1cbiAgICB9LFxuICAgIHJlc29sdmU6IHsgYWxpYXMgfSxcbiAgICBwbHVnaW5zLFxuICAgIGNzczoge1xuICAgICAgcHJlcHJvY2Vzc29yT3B0aW9uczoge1xuICAgICAgICBzY3NzOiB7XG4gICAgICAgICAgYXBpOiAnbW9kZXJuLWNvbXBpbGVyJyxcbiAgICAgICAgICBhZGRpdGlvbmFsRGF0YTogYEB1c2UgXCJAL3N0eWxlcy92YXJpYWJsZXMuc2Nzc1wiIGFzICo7YCxcbiAgICAgICAgICBzaWxlbmNlRGVwcmVjYXRpb25zOiBbJ2xlZ2FjeS1qcy1hcGknXVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfSxcbiAgICBvcHRpbWl6ZURlcHM6IHtcbiAgICAgIGluY2x1ZGU6IFtcbiAgICAgICAgJ2VjaGFydHMvY29yZScsXG4gICAgICAgICdlY2hhcnRzL2NoYXJ0cycsXG4gICAgICAgICdlY2hhcnRzL3JlbmRlcmVycycsXG4gICAgICAgICdlY2hhcnRzL2NvbXBvbmVudHMnLFxuICAgICAgICAndnVlLWVjaGFydHMnLFxuICAgICAgICAnZWNoYXJ0cy13b3JkY2xvdWQnLFxuICAgICAgICAndnVlZHJhZ2dhYmxlJyxcbiAgICAgICAgJ3NvcnRhYmxlanMnLFxuICAgICAgICAneGxzeCdcbiAgICAgIF1cbiAgICB9LFxuICAgIGJ1aWxkOiB7XG4gICAgICB0YXJnZXQ6ICdlczIwMTUnLFxuICAgICAgY2h1bmtTaXplV2FybmluZ0xpbWl0OiAyMDAwLFxuICAgICAgcm9sbHVwT3B0aW9uczoge1xuICAgICAgICBvdXRwdXQ6IHtcbiAgICAgICAgICBtYW51YWxDaHVua3M6IHVuZGVmaW5lZFxuICAgICAgICB9LFxuICAgICAgICBvbndhcm4od2FybmluZywgd2Fybikge1xuICAgICAgICAgIC8vIFx1NUZGRFx1NzU2NVx1NjdEMFx1NEU5Qlx1OEI2Nlx1NTQ0QVxuICAgICAgICAgIGlmICh3YXJuaW5nLmNvZGUgPT09ICdDSVJDVUxBUl9ERVBFTkRFTkNZJykgcmV0dXJuO1xuICAgICAgICAgIGlmICh3YXJuaW5nLmNvZGUgPT09ICdUSElTX0lTX1VOREVGSU5FRCcpIHJldHVybjtcbiAgICAgICAgICB3YXJuKHdhcm5pbmcpO1xuICAgICAgICB9LFxuICAgICAgICB0cmVlc2hha2U6IGZhbHNlXG4gICAgICB9XG4gICAgfVxuICB9O1xufSk7XG4iXSwKICAibWFwcGluZ3MiOiAiO0FBQWdXLFNBQVMsb0JBQW9CO0FBQzdYLE9BQU8sU0FBUztBQUNoQixTQUFTLGVBQWU7QUFDeEIsT0FBTyxpQkFBaUI7QUFDeEIsT0FBTyxnQkFBZ0I7QUFDdkIsU0FBUywyQkFBMkI7QUFDcEMsU0FBUyx3QkFBd0I7QUFJakMsSUFBTyxzQkFBUSxhQUFhLENBQUMsRUFBRSxRQUFRLE1BQU07QUFDM0MsUUFBTSxVQUFVLFlBQVk7QUFDNUIsUUFBTSxRQUFRO0FBQUEsSUFDWixNQUFNLFFBQVEsS0FBSyxJQUFJO0FBQUEsSUFDdkIsWUFBWTtBQUFBLEVBQ2Q7QUFFQSxRQUFNLFVBQVUsQ0FBQyxJQUFJLENBQUM7QUFDdEIsTUFBSSxTQUFTO0FBRVgsWUFBUTtBQUFBLE1BQ04sV0FBVztBQUFBLFFBQ1QsS0FBSztBQUFBLFFBQ0wsV0FBVztBQUFBLFVBQ1Qsb0JBQW9CO0FBQUEsWUFDbEIsYUFBYTtBQUFBLFVBQ2YsQ0FBQztBQUFBLFVBQ0QsaUJBQWlCO0FBQUEsWUFDZixhQUFhO0FBQUEsVUFDZixDQUFDO0FBQUEsUUFDSDtBQUFBLE1BQ0YsQ0FBQztBQUFBLElBQ0g7QUFFQSxZQUFRO0FBQUEsTUFDTixZQUFZO0FBQUEsUUFDVixTQUFTLENBQUM7QUFBQSxRQUNWLFdBQVc7QUFBQSxRQUNYLFdBQVc7QUFBQSxRQUNYLEtBQUs7QUFBQSxNQUNQLENBQUM7QUFBQSxJQUNIO0FBQUEsRUFDRixPQUFPO0FBRUwsVUFBTSxhQUFhLElBQUk7QUFBQSxFQUN6QjtBQUNBLFNBQU87QUFBQSxJQUNMLFFBQVE7QUFBQSxNQUNOLE1BQU07QUFBQTtBQUFBLE1BRU4sT0FBTztBQUFBO0FBQUEsUUFFTCxRQUFRO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBLE1BU1Y7QUFBQSxJQUNGO0FBQUEsSUFDQSxTQUFTLEVBQUUsTUFBTTtBQUFBLElBQ2pCO0FBQUEsSUFDQSxLQUFLO0FBQUEsTUFDSCxxQkFBcUI7QUFBQSxRQUNuQixNQUFNO0FBQUEsVUFDSixLQUFLO0FBQUEsVUFDTCxnQkFBZ0I7QUFBQSxVQUNoQixxQkFBcUIsQ0FBQyxlQUFlO0FBQUEsUUFDdkM7QUFBQSxNQUNGO0FBQUEsSUFDRjtBQUFBLElBQ0EsY0FBYztBQUFBLE1BQ1osU0FBUztBQUFBLFFBQ1A7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLFFBQ0E7QUFBQSxRQUNBO0FBQUEsUUFDQTtBQUFBLE1BQ0Y7QUFBQSxJQUNGO0FBQUEsSUFDQSxPQUFPO0FBQUEsTUFDTCxRQUFRO0FBQUEsTUFDUix1QkFBdUI7QUFBQSxNQUN2QixlQUFlO0FBQUEsUUFDYixRQUFRO0FBQUEsVUFDTixjQUFjO0FBQUEsUUFDaEI7QUFBQSxRQUNBLE9BQU8sU0FBUyxNQUFNO0FBRXBCLGNBQUksUUFBUSxTQUFTO0FBQXVCO0FBQzVDLGNBQUksUUFBUSxTQUFTO0FBQXFCO0FBQzFDLGVBQUssT0FBTztBQUFBLFFBQ2Q7QUFBQSxRQUNBLFdBQVc7QUFBQSxNQUNiO0FBQUEsSUFDRjtBQUFBLEVBQ0Y7QUFDRixDQUFDOyIsCiAgIm5hbWVzIjogW10KfQo=
